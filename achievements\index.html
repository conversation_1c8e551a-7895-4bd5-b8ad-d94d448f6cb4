<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Th<PERSON><PERSON> - <PERSON>on</title>
    <link rel="stylesheet" href="../assets/css/styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        .page-header {
            text-align: center;
            margin: 120px 0 50px;
        }

        .page-header h1 {
            font-size: 2.5rem;
            color: #333;
            margin-bottom: 15px;
        }

        .page-header p {
            color: #666;
            font-size: 1.2rem;
            max-width: 700px;
            margin: 0 auto;
        }

        .achievements-container {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 30px;
            margin: 30px 0 50px;
        }

        .achievement-card {
            background-color: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            transition: transform 0.3s, box-shadow 0.3s;
            cursor: pointer;
            position: relative;
        }

        .achievement-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        }

        .achievement-image {
            height: 220px;
            overflow: hidden;
            position: relative;
        }

        .achievement-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.5s;
        }

        .achievement-card:hover .achievement-image img {
            transform: scale(1.05);
        }

        .achievement-details {
            padding: 25px;
        }

        .achievement-date {
            color: #4285F4;
            font-size: 0.9rem;
            margin-bottom: 10px;
            font-weight: 500;
        }

        .achievement-title {
            font-size: 1.4rem;
            color: #333;
            margin-bottom: 12px;
            font-weight: 700;
            line-height: 1.3;
        }

        .achievement-excerpt {
            color: #666;
            margin-bottom: 20px;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
            line-height: 1.5;
        }

        .achievement-stats {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-top: 15px;
            border-top: 1px solid #f0f0f0;
        }

        .stat-item {
            display: flex;
            align-items: center;
            color: #666;
            font-size: 0.9rem;
        }

        .stat-item i {
            margin-right: 6px;
            font-size: 1rem;
        }

        .like-btn {
            background: none;
            border: none;
            cursor: pointer;
            display: flex;
            align-items: center;
            color: #666;
            font-size: 0.9rem;
            transition: color 0.3s;
            padding: 5px 10px;
            border-radius: 20px;
            transition: all 0.3s;
        }

        .like-btn:hover {
            background-color: #f8f9fa;
            color: #4285F4;
        }

        .like-btn.liked {
            color: #e74c3c;
        }

        .like-btn.liked i {
            color: #e74c3c;
        }

        .like-btn i {
            margin-right: 6px;
            font-size: 1rem;
            transition: color 0.3s;
        }

        .read-more {
            color: #4285F4;
            font-weight: 500;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            padding: 8px 0;
        }

        .read-more i {
            margin-left: 5px;
            transition: transform 0.3s;
        }

        .read-more:hover i {
            transform: translateX(3px);
        }

        .loading-spinner {
            text-align: center;
            padding: 50px;
            color: #666;
        }

        .loading-spinner i {
            font-size: 2rem;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .no-achievements {
            grid-column: 1/-1;
            text-align: center;
            padding: 50px;
            color: #666;
        }

        .no-achievements i {
            font-size: 3rem;
            margin-bottom: 20px;
            color: #ddd;
        }
        
        .modal {
            display: none;
            position: fixed;
            z-index: 1100;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            overflow: auto;
            background-color: rgba(0, 0, 0, 0.7);
            opacity: 0;
            transition: opacity 0.3s;
        }

        .modal.active {
            display: block;
            opacity: 1;
        }

        .modal-content {
            background-color: white;
            margin: 3% auto;
            padding: 30px;
            border-radius: 15px;
            width: 85%;
            max-width: 900px;
            box-shadow: 0 5px 30px rgba(0, 0, 0, 0.3);
            position: relative;
            transform: translateY(-50px);
            opacity: 0;
            transition: all 0.4s;
            max-height: 90vh;
            overflow-y: auto;
        }

        .modal.active .modal-content {
            transform: translateY(0);
            opacity: 1;
        }

        .close-modal {
            position: absolute;
            top: 15px;
            right: 15px;
            font-size: 1.5rem;
            color: #999;
            cursor: pointer;
            transition: color 0.3s;
            z-index: 10;
            background: white;
            border-radius: 50%;
            width: 35px;
            height: 35px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .close-modal:hover {
            color: #333;
            background: #f8f9fa;
        }

        .modal-images {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-bottom: 25px;
        }

        .modal-image {
            width: 100%;
            height: 200px;
            object-fit: cover;
            border-radius: 10px;
            cursor: pointer;
            transition: transform 0.3s;
        }

        .modal-image:hover {
            transform: scale(1.02);
        }

        .modal-header {
            margin-bottom: 25px;
        }

        .modal-title {
            font-size: 2rem;
            margin-bottom: 10px;
            color: #333;
            line-height: 1.3;
        }

        .modal-date {
            color: #4285F4;
            font-size: 1rem;
            margin-bottom: 15px;
            font-weight: 500;
        }

        .modal-stats {
            display: flex;
            gap: 25px;
            margin-bottom: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 10px;
        }

        .modal-stat {
            display: flex;
            align-items: center;
            color: #666;
        }

        .modal-stat i {
            margin-right: 8px;
            font-size: 1.1rem;
        }

        .modal-like-btn {
            background: linear-gradient(135deg, #4285F4, #ff7aa8);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            cursor: pointer;
            display: flex;
            align-items: center;
            font-weight: 500;
            transition: all 0.3s;
            margin-bottom: 20px;
        }

        .modal-like-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(66, 133, 244, 0.3);
        }

        .modal-like-btn.liked {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
        }

        .modal-like-btn i {
            margin-right: 8px;
            font-size: 1.1rem;
        }

        .modal-content p {
            margin-bottom: 15px;
            line-height: 1.7;
            color: #444;
        }

        .modal-content ul {
            margin-bottom: 20px;
            padding-left: 20px;
        }

        .modal-content li {
            margin-bottom: 8px;
            line-height: 1.6;
            color: #444;
        }

        /* New template styles */
        .project-info-section {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            padding: 25px;
            border-radius: 15px;
            margin-bottom: 30px;
            border-left: 5px solid #4285F4;
        }

        .info-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 15px;
            margin-top: 20px;
        }

        .info-item {
            background: white;
            padding: 15px;
            border-radius: 10px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transition: transform 0.3s;
        }

        .info-item:hover {
            transform: translateY(-2px);
        }

        .feature-list {
            margin: 20px 0;
        }

        .feature-item {
            background: #f8f9fa;
            padding: 20px;
            margin-bottom: 15px;
            border-radius: 12px;
            border-left: 4px solid #4285F4;
            transition: all 0.3s;
        }

        .feature-item:hover {
            background: #e3f2fd;
            transform: translateX(5px);
        }

        .certificate-info {
            background: linear-gradient(135deg, #4285F4, #34a853);
            color: white;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            margin-top: 30px;
        }

        .certificate-info h3 {
            margin: 0;
            font-size: 1.2rem;
            font-weight: 600;
        }

        @media (max-width: 768px) {
            .modal-content {
                width: 95%;
                padding: 20px;
                margin: 5% auto;
            }

            .modal-title {
                font-size: 1.5rem;
            }

            .modal-stats {
                flex-direction: column;
                gap: 10px;
            }

            .modal-images {
                grid-template-columns: 1fr;
            }

            .info-grid {
                grid-template-columns: 1fr;
            }

            .feature-item {
                padding: 15px;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation Bar -->
    <header>
        <div class="container">
            <div class="logo">
                <img src="../assets/images/logo.jpg" alt="VTA Logo">
            </div>
            <nav>
                <ul>
                    <li><a href="../index.html">Trang Chủ</a></li>
                    <li><a href="../classes/">Lớp Học</a></li>
                    <li><a href="index.html" class="active">Thành Tích</a></li>
                    <li><a href="../auth/register.html">Đăng Ký</a></li>
                    <li><a href="../rankings/">Bảng Xếp Hạng</a></li>
                    <li><a href="../research/">Nghiên Cứu</a></li>
                    <li><a href="../auth/">Tài Khoản</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <!-- Achievements Section -->
    <section>
        <div class="container">
            <div class="page-header">
                <h1>Thành Tích Học Viên</h1>
                <p>Tự hào về những thành tựu xuất sắc của học viên Vthon trong lĩnh vực lập trình Python và AI</p>
            </div>

            <div class="achievements-container" id="achievementsContainer">
                <!-- Loading spinner -->
                <div class="loading-spinner" id="loadingSpinner">
                    <i class="fas fa-spinner"></i>
                    <p>Đang tải thành tích...</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Modal for Achievement Details -->
    <div class="modal" id="achievementModal">
        <div class="modal-content">
            <span class="close-modal"><i class="fas fa-times"></i></span>
            <div class="modal-header">
                <h2 class="modal-title"></h2>
                <div class="modal-date"></div>
                <div class="modal-stats">
                    <div class="modal-stat">
                        <i class="fas fa-eye"></i>
                        <span id="modalViewCount">0</span> lượt xem
                    </div>
                    <div class="modal-stat">
                        <i class="fas fa-heart"></i>
                        <span id="modalLikeCount">0</span> lượt thích
                    </div>
                </div>
                <button class="modal-like-btn" id="modalLikeBtn">
                    <i class="fas fa-heart"></i>
                    <span>Thích bài viết</span>
                </button>
            </div>
            <div class="modal-images" id="modalImages"></div>
            <div class="modal-body" id="modalBody"></div>
        </div>
    </div>

    <!-- Footer -->
    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-section footer-logo">
                    <div class="footer-logo-container">
                        <img src="../assets/images/logo.jpg" alt="VTA Logo">
                        <div class="logo-text">Vthon Academy</div>
                    </div>
                    <div class="slogan">Học, học nữa, học mãi.</div>
                    <div class="footer-bottom">
                        <p>&copy; 2025 – All rights reserved.</p>
                    </div>
                </div>

                <div class="footer-section footer-contact">
                    <h3>Liên hệ</h3>
                    <p><i class="fas fa-envelope"></i> <EMAIL></p>
                    <p><i class="fas fa-phone"></i> 0399787678</p>
                    <p><i class="fab fa-facebook-messenger"></i> Zalo: 0399787678</p>
                </div>

                <div class="footer-section footer-social">
                    <h3>Mạng xã hội</h3>
                    <div class="social-links">
                        <a href="https://www.facebook.com/vinhle030904/" target="_blank" class="social-link facebook">
                            <i class="fab fa-facebook-f"></i> Facebook
                        </a>
                        <a href="https://www.tiktok.com/@sunnii39" target="_blank" class="social-link tiktok">
                            <i class="fab fa-tiktok"></i> TikTok
                        </a>
                        <a href="https://www.instagram.com/vlee.39/?hl=en" target="_blank" class="social-link instagram">
                            <i class="fab fa-instagram"></i> Instagram
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <script src="../assets/js/script.js"></script>
    <script type="module">
        // Import the achievements manager - it will auto-initialize
        import { achievementsManager } from './js/achievements-manager.js';

        // Make the manager globally available for debugging
        window.achievementsManager = achievementsManager;

        console.log('Achievements page loaded successfully');
    </script>
</body>
</html>
