<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON><PERSON> <PERSON> VT Academy</title>
    <link rel="stylesheet" href="../../assets/css/style.css">
    <link rel="stylesheet" href="../css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .fees-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
        }

        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid #ecf0f1;
        }

        .page-header h1 {
            color: #2c3e50;
            margin: 0;
            font-size: 2rem;
            font-weight: 700;
        }

        .back-btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 10px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .back-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .fee-summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .summary-card {
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .summary-card h3 {
            margin: 0 0 0.5rem 0;
            font-size: 1.8rem;
            font-weight: 700;
        }

        .summary-card p {
            margin: 0;
            color: #7f8c8d;
            font-weight: 500;
        }

        .summary-card:nth-child(1) h3 { color: #3498db; }
        .summary-card:nth-child(2) h3 { color: #27ae60; }
        .summary-card:nth-child(3) h3 { color: #e74c3c; }
        .summary-card:nth-child(4) h3 { color: #f39c12; }
        .summary-card:nth-child(5) h3 { color: #9b59b6; }

        .controls-section {
            margin-bottom: 2rem;
        }

        .view-mode-controls {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
            padding: 1rem;
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border-radius: 10px;
            border: 2px solid #dee2e6;
        }

        .mode-toggle {
            display: flex;
            gap: 0.5rem;
        }

        .mode-btn {
            padding: 0.75rem 1.5rem;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            background: white;
            color: #6c757d;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .mode-btn:hover {
            border-color: #3498db;
            color: #3498db;
        }

        .mode-btn.active {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            border-color: #3498db;
        }

        .history-selector {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .filter-controls {
            display: flex;
            gap: 1rem;
            margin-bottom: 1rem;
            flex-wrap: wrap;
        }

        .month-controls {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            padding: 1rem;
            border-radius: 10px;
            border: 2px solid #dee2e6;
        }

        .current-month h3 {
            margin: 0;
            color: #2c3e50;
            font-size: 1.2rem;
        }

        .finalize-btn {
            background: linear-gradient(135deg, #27ae60, #2ecc71);
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 10px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .finalize-btn:hover {
            background: linear-gradient(135deg, #229954, #27ae60);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(39, 174, 96, 0.3);
        }

        .filter-select {
            padding: 0.75rem 1rem;
            border: 2px solid #ecf0f1;
            border-radius: 10px;
            font-size: 1rem;
            background: white;
            min-width: 150px;
        }

        .fees-table-container {
            overflow-x: auto;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .fees-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            min-width: 800px;
        }

        .fees-table th,
        .fees-table td {
            padding: 1rem;
            text-align: center;
            border-bottom: 1px solid #ecf0f1;
        }

        .fees-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #2c3e50;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .fees-table th:first-child,
        .fees-table td:first-child {
            text-align: left;
            position: sticky;
            left: 0;
            background: white;
            z-index: 5;
            box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1);
        }

        .fees-table th:first-child {
            background: #f8f9fa;
            z-index: 15;
        }

        .student-info {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            min-width: 200px;
        }

        .student-avatar {
            width: 35px;
            height: 35px;
            border-radius: 50%;
            object-fit: cover;
        }

        .student-details {
            text-align: left;
        }

        .student-name {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 0.25rem;
        }

        .student-class {
            font-size: 0.8rem;
            color: #7f8c8d;
        }

        .fee-checkbox {
            width: 20px;
            height: 20px;
            accent-color: #27ae60;
            cursor: pointer;
        }

        .month-header {
            writing-mode: vertical-rl;
            text-orientation: mixed;
            min-width: 80px;
        }

        .paid-cell {
            background: #d4edda !important;
        }

        .unpaid-cell {
            background: #f8d7da !important;
        }

        .loading {
            text-align: center;
            padding: 2rem;
            color: #7f8c8d;
        }

        .no-students {
            text-align: center;
            padding: 3rem;
            color: #7f8c8d;
        }

        .no-students i {
            font-size: 3rem;
            margin-bottom: 1rem;
            color: #bdc3c7;
        }

        @media (max-width: 768px) {
            .fee-summary {
                grid-template-columns: 1fr 1fr;
            }
            
            .filter-controls {
                flex-direction: column;
            }
            
            .fees-table th,
            .fees-table td {
                padding: 0.5rem;
                font-size: 0.9rem;
            }
            
            .student-info {
                min-width: 150px;
            }
            
            .month-header {
                min-width: 60px;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header>
        <div class="container">
            <div class="logo">
                <img src="../../assets/images/logo.jpg" alt="VTA Logo">
            </div>
            <nav>
                <ul>
                    <li><a href="../../index.html">Trang Chủ</a></li>
                    <li><a href="../../classes/">Lớp Học</a></li>
                    <li><a href="../../achievements/">Thành Tích</a></li>
                    <li><a href="../../auth/register.html">Đăng Ký</a></li>
                    <li><a href="../../rankings/">Bảng Xếp Hạng</a></li>
                    <li><a href="../../research/">Nghiên Cứu</a></li>
                    <li><a href="../../auth/">Tài Khoản</a></li>
                    <li><a href="../" class="active">Quản Trị</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <!-- Main Content -->
    <main class="admin-main">
        <div class="container">
            <div class="fees-container">
                <div class="page-header">
                    <h1><i class="fas fa-credit-card"></i> Quản Lý Học Phí</h1>
                    <a href="../" class="back-btn">
                        <i class="fas fa-arrow-left"></i> Quay lại
                    </a>
                </div>

                <!-- Fee Summary -->
                <div class="fee-summary">
                    <div class="summary-card">
                        <h3 id="totalStudents">0</h3>
                        <p>Tổng học viên</p>
                    </div>
                    <div class="summary-card">
                        <h3 id="paidThisMonth">0</h3>
                        <p>Đã đóng tháng này</p>
                    </div>
                    <div class="summary-card">
                        <h3 id="unpaidThisMonth">0</h3>
                        <p>Chưa đóng tháng này</p>
                    </div>
                    <div class="summary-card">
                        <h3 id="monthlyRevenue">0 ₫</h3>
                        <p>Thu nhập tháng hiện tại</p>
                    </div>
                    <div class="summary-card">
                        <h3 id="totalRevenue">0 ₫</h3>
                        <p>Tổng thu nhập tất cả</p>
                    </div>
                </div>

                <!-- Controls -->
                <div class="controls-section">
                    <!-- View Mode Toggle -->
                    <div class="view-mode-controls">
                        <div class="mode-toggle">
                            <button id="currentModeBtn" class="mode-btn active">
                                <i class="fas fa-calendar-check"></i> Quản lý hiện tại
                            </button>
                            <button id="historyModeBtn" class="mode-btn">
                                <i class="fas fa-history"></i> Xem lịch sử
                            </button>
                        </div>
                        <div id="historySelector" class="history-selector" style="display: none;">
                            <select id="historyMonthSelect" class="filter-select">
                                <option value="">Chọn tháng để xem</option>
                            </select>
                        </div>
                    </div>

                    <!-- Filter Controls -->
                    <div class="filter-controls">
                        <select id="classFilter" class="filter-select">
                            <option value="">Tất cả lớp học</option>
                            <option value="python-a">Python & AI - Lớp A</option>
                            <option value="python-b">Python & AI - Lớp B</option>
                            <option value="python-c">Python & AI - Lớp C</option>
                        </select>
                        <select id="statusFilter" class="filter-select">
                            <option value="">Tất cả trạng thái</option>
                            <option value="paid">Đã đóng</option>
                            <option value="unpaid">Chưa đóng</option>
                        </select>
                    </div>

                    <!-- Month Controls -->
                    <div class="month-controls">
                        <div class="current-month">
                            <h3 id="monthTitle">Quản lý học phí: <span id="currentMonthDisplay">Tháng 6 năm 2025</span></h3>
                        </div>
                        <button id="finalizeMonthBtn" class="finalize-btn">
                            <i class="fas fa-check-circle"></i> Kết Toán Tháng
                        </button>
                    </div>
                </div>

                <!-- Fees Table -->
                <div class="fees-table-container">
                    <div id="feesTableContainer">
                        <div class="loading">
                            <i class="fas fa-spinner fa-spin"></i>
                            <p>Đang tải dữ liệu học phí...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-section footer-logo">
                    <div class="footer-logo-container">
                        <img src="../../assets/images/logo.jpg" alt="VTA Logo">
                        <div class="logo-text">Vthon Academy</div>
                    </div>
                    <div class="slogan">Học, học nữa, học mãi.</div>
                    <div class="footer-bottom">
                        <p>&copy; 2025 – All rights reserved.</p>
                    </div>
                </div>

                <div class="footer-section footer-contact">
                    <h3>Liên hệ</h3>
                    <p><i class="fas fa-envelope"></i> <EMAIL></p>
                    <p><i class="fas fa-phone"></i> 0399787678</p>
                    <p><i class="fab fa-facebook-messenger"></i> Zalo: 0399787678</p>
                </div>

                <div class="footer-section footer-social">
                    <h3>Mạng xã hội</h3>
                    <div class="social-links">
                        <a href="https://www.facebook.com/vinhle030904/" target="_blank" class="social-link facebook">
                            <i class="fab fa-facebook-f"></i> Facebook
                        </a>
                        <a href="https://www.tiktok.com/@sunnii39" target="_blank" class="social-link tiktok">
                            <i class="fab fa-tiktok"></i> TikTok
                        </a>
                        <a href="https://www.instagram.com/vlee.39/?hl=en" target="_blank" class="social-link instagram">
                            <i class="fab fa-instagram"></i> Instagram
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Firebase SDK -->
    <script type="module">
        // Import Firebase modules
        import { initializeApp } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-app.js";
        import { getAuth, onAuthStateChanged } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-auth.js";
        import {
            getFirestore,
            doc,
            getDoc,
            setDoc,
            updateDoc,
            collection,
            query,
            getDocs,
            where,
            orderBy,
            writeBatch
        } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-firestore.js";

        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyBKNo8y_MOKYc3f3UdNRFwcMgeLRW71WXA",
            authDomain: "classroom-web-48bc2.firebaseapp.com",
            projectId: "classroom-web-48bc2",
            storageBucket: "classroom-web-48bc2.firebasestorage.app",
            messagingSenderId: "446746787502",
            appId: "1:446746787502:web:48d5ffd5a0b2c6e043b73f",
            measurementId: "G-742XRP9E96"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const auth = getAuth(app);
        const db = getFirestore(app);

        let studentsData = [];
        let feePayments = [];
        let monthlyReports = [];
        let currentViewMode = 'current'; // 'current' or 'history'
        let selectedHistoryMonth = null;

        // Check admin access
        onAuthStateChanged(auth, async (user) => {
            if (!user || user.email !== '<EMAIL>') {
                window.location.href = '../../auth/';
                return;
            }

            await initializeFeeManagement();
        });

        // Initialize fee management
        async function initializeFeeManagement() {
            try {
                // Load system settings first
                await loadSystemSettings();

                // Load students and fee data
                await loadStudentsData();
                await loadFeePayments();

                // Initialize fee payments for all students
                await initializeFeePayments();

                // Load monthly reports for history
                await loadMonthlyReports();

                // Display data
                displayFeeTable();
                updateSummary();
                setupEventListeners();

            } catch (error) {
                console.error('Error initializing fee management:', error);
                showError('Lỗi khi khởi tạo quản lý học phí');
            }
        }



        // Load students data
        async function loadStudentsData() {
            const usersQuery = query(collection(db, "users"));
            const usersSnapshot = await getDocs(usersQuery);
            
            studentsData = [];
            usersSnapshot.forEach((doc) => {
                const userData = doc.data();
                if (userData.email !== '<EMAIL>' && userData.fullName) {
                    studentsData.push({
                        id: doc.id,
                        ...userData
                    });
                }
            });
        }

        // Load fee payments
        async function loadFeePayments() {
            const paymentsQuery = query(collection(db, "feePayments"));
            const paymentsSnapshot = await getDocs(paymentsQuery);

            feePayments = [];
            paymentsSnapshot.forEach((doc) => {
                feePayments.push({
                    id: doc.id,
                    ...doc.data()
                });
            });
        }

        // Load monthly reports
        async function loadMonthlyReports() {
            try {
                const reportsQuery = query(collection(db, "monthlyReports"), orderBy("month", "desc"));
                const reportsSnapshot = await getDocs(reportsQuery);

                monthlyReports = [];
                reportsSnapshot.forEach((doc) => {
                    monthlyReports.push({
                        id: doc.id,
                        ...doc.data()
                    });
                });

                // Update history dropdown
                updateHistoryDropdown();
            } catch (error) {
                console.error('Error loading monthly reports:', error);
            }
        }

        // Save monthly report
        async function saveMonthlyReport(reportData) {
            try {
                await setDoc(doc(db, "monthlyReports", reportData.month), reportData);
                monthlyReports.unshift(reportData); // Add to beginning of array
                updateHistoryDropdown();
            } catch (error) {
                console.error('Error saving monthly report:', error);
                throw error;
            }
        }

        // Initialize fee payments for all students for current month
        async function initializeFeePayments() {
            const currentMonth = getCurrentMonth();

            for (const student of studentsData) {
                const paymentId = `${student.id}_${currentMonth.key}`;
                const existingPayment = feePayments.find(p => p.id === paymentId);

                if (!existingPayment) {
                    const paymentData = {
                        studentId: student.id,
                        studentName: student.fullName,
                        classId: student.courseClass || 'no-class',
                        month: currentMonth.key,
                        isPaid: false,
                        amount: 250000,
                        createdAt: new Date().toISOString()
                    };

                    await setDoc(doc(db, "feePayments", paymentId), paymentData);
                    feePayments.push({ id: paymentId, ...paymentData });
                }
            }
        }

        // System settings management
        let systemSettings = {
            currentMonth: '2025-06',
            currentMonthName: 'Tháng 6 năm 2025'
        };

        // Load system settings from Firebase
        async function loadSystemSettings() {
            try {
                const settingsDoc = await getDoc(doc(db, "systemSettings", "feeManagement"));
                if (settingsDoc.exists()) {
                    systemSettings = settingsDoc.data();
                } else {
                    // Initialize default settings
                    await setDoc(doc(db, "systemSettings", "feeManagement"), systemSettings);
                }

                // Update UI
                document.getElementById('currentMonthDisplay').textContent = systemSettings.currentMonthName;
            } catch (error) {
                console.error('Error loading system settings:', error);
            }
        }

        // Save system settings to Firebase
        async function saveSystemSettings() {
            try {
                await setDoc(doc(db, "systemSettings", "feeManagement"), systemSettings);
            } catch (error) {
                console.error('Error saving system settings:', error);
                throw error;
            }
        }

        // Get current month info
        function getCurrentMonth() {
            return {
                key: systemSettings.currentMonth,
                name: systemSettings.currentMonthName
            };
        }

        // Get next month info
        function getNextMonth() {
            const currentDate = new Date(systemSettings.currentMonth + '-01');
            currentDate.setMonth(currentDate.getMonth() + 1);

            const nextMonthKey = currentDate.toISOString().slice(0, 7);
            const nextMonthName = currentDate.toLocaleDateString('vi-VN', { year: 'numeric', month: 'long' });

            return {
                key: nextMonthKey,
                name: `Tháng ${currentDate.getMonth() + 1} năm ${currentDate.getFullYear()}`
            };
        }

        // Get filtered students based on current filters
        function getFilteredStudents() {
            const classFilter = document.getElementById('classFilter').value;
            const statusFilter = document.getElementById('statusFilter').value;
            const currentMonth = getCurrentMonth();

            let filteredStudents = [...studentsData];

            // Apply class filter
            if (classFilter) {
                filteredStudents = filteredStudents.filter(student => {
                    const studentClasses = getStudentClasses(student);
                    return studentClasses.includes(classFilter);
                });
            }

            // Apply status filter
            if (statusFilter) {
                filteredStudents = filteredStudents.filter(student => {
                    const paymentId = `${student.id}_${currentMonth.key}`;
                    const payment = feePayments.find(p => p.id === paymentId);
                    const isPaid = payment ? payment.isPaid : false;

                    if (statusFilter === 'paid') {
                        return isPaid;
                    } else if (statusFilter === 'unpaid') {
                        return !isPaid;
                    }
                    return true;
                });
            }

            return filteredStudents;
        }

        // Display fee table
        function displayFeeTable() {
            const container = document.getElementById('feesTableContainer');

            if (studentsData.length === 0) {
                container.innerHTML = `
                    <div class="no-students">
                        <i class="fas fa-users"></i>
                        <h3>Chưa có học viên nào</h3>
                        <p>Dữ liệu học phí sẽ hiển thị khi có học viên đăng ký</p>
                    </div>
                `;
                return;
            }

            const currentMonth = getCurrentMonth();
            const filteredStudents = getFilteredStudents();

            if (filteredStudents.length === 0) {
                container.innerHTML = `
                    <div class="no-students">
                        <i class="fas fa-filter"></i>
                        <h3>Không có học viên nào phù hợp</h3>
                        <p>Thử thay đổi bộ lọc để xem thêm học viên</p>
                    </div>
                `;
                return;
            }

            let tableHTML = `
                <table class="fees-table">
                    <thead>
                        <tr>
                            <th>Học viên</th>
                            <th class="month-header">${currentMonth.name}</th>
                        </tr>
                    </thead>
                    <tbody>
            `;

            // Add student rows
            filteredStudents.forEach(student => {
                const studentClasses = getStudentClasses(student);
                const classNames = studentClasses.map(cls => getClassName(cls)).join(', ') || 'Chưa chọn lớp';

                tableHTML += `
                    <tr>
                        <td>
                            <div class="student-info">
                                <img src="${student.avatar ? student.avatar.replace('../assets/', '../../assets/') : '../../assets/images/avatars/avatar_boy_1.png'}"
                                     alt="Avatar" class="student-avatar">
                                <div class="student-details">
                                    <div class="student-name">${student.fullName}</div>
                                    <div class="student-class">${classNames}</div>
                                </div>
                            </div>
                        </td>
                `;

                // Add payment checkbox for current month only
                const paymentId = `${student.id}_${currentMonth.key}`;
                const payment = feePayments.find(p => p.id === paymentId);
                const isPaid = payment ? payment.isPaid : false;
                const cellClass = isPaid ? 'paid-cell' : 'unpaid-cell';

                tableHTML += `
                    <td class="${cellClass}">
                        <input type="checkbox" class="fee-checkbox"
                               data-student-id="${student.id}"
                               data-month="${currentMonth.key}"
                               ${isPaid ? 'checked' : ''}
                               onchange="updatePaymentStatus('${student.id}', '${currentMonth.key}', this.checked)">
                    </td>
                `;

                tableHTML += `</tr>`;
            });

            tableHTML += `
                    </tbody>
                </table>
            `;

            container.innerHTML = tableHTML;
        }

        // Update payment status
        window.updatePaymentStatus = async function(studentId, month, isPaid) {
            try {
                const paymentId = `${studentId}_${month}`;
                const student = studentsData.find(s => s.id === studentId);

                console.log('Updating payment status:', {studentId, month, isPaid, paymentId});

                const paymentData = {
                    studentId: studentId,
                    studentName: student.fullName,
                    classId: student.courseClass || 'no-class',
                    month: month,
                    isPaid: isPaid,
                    amount: 250000,
                    updatedAt: new Date().toISOString()
                };

                if (isPaid) {
                    paymentData.paidDate = new Date().toISOString();
                }

                console.log('Saving to Firebase:', paymentData);
                await setDoc(doc(db, "feePayments", paymentId), paymentData);
                console.log('Firebase save successful');
                
                // Update local data
                const existingPaymentIndex = feePayments.findIndex(p => p.id === paymentId);
                if (existingPaymentIndex !== -1) {
                    feePayments[existingPaymentIndex] = { id: paymentId, ...paymentData };
                } else {
                    feePayments.push({ id: paymentId, ...paymentData });
                }

                // Update cell appearance
                const checkbox = document.querySelector(`input[data-student-id="${studentId}"][data-month="${month}"]`);
                const cell = checkbox.parentElement;
                cell.className = isPaid ? 'paid-cell' : 'unpaid-cell';

                // Update summary
                updateSummary();

                const statusText = isPaid ? 'đã đóng' : 'chưa đóng';
                const currentMonth = getCurrentMonth();
                const monthName = currentMonth.key === month ? currentMonth.name : month;
                showSuccess(`Cập nhật trạng thái ${statusText} cho ${student.fullName} - ${monthName}`);

            } catch (error) {
                console.error('Error updating payment status:', error);
                showError('Lỗi khi cập nhật trạng thái thanh toán');
            }
        };

        // Update summary statistics
        function updateSummary() {
            const currentMonth = getCurrentMonth();
            const filteredStudents = getFilteredStudents();

            // Calculate stats based on filtered students
            const totalStudents = filteredStudents.length;
            let paidThisMonth = 0;

            filteredStudents.forEach(student => {
                const paymentId = `${student.id}_${currentMonth.key}`;
                const payment = feePayments.find(p => p.id === paymentId);
                if (payment && payment.isPaid) {
                    paidThisMonth++;
                }
            });

            const unpaidThisMonth = totalStudents - paidThisMonth;
            const monthlyRevenue = paidThisMonth * 250000;

            // Calculate total revenue from all monthly reports + current month
            const totalFromReports = monthlyReports.reduce((sum, r) => sum + (r.totalRevenue || 0), 0);
            const totalRevenue = totalFromReports + monthlyRevenue;

            document.getElementById('totalStudents').textContent = totalStudents;
            document.getElementById('paidThisMonth').textContent = paidThisMonth;
            document.getElementById('unpaidThisMonth').textContent = unpaidThisMonth;
            document.getElementById('monthlyRevenue').textContent = formatCurrency(monthlyRevenue);
            document.getElementById('totalRevenue').textContent = formatCurrency(totalRevenue);

            console.log('UpdateSummary - Current month:', currentMonth.key, 'Paid:', paidThisMonth, 'Total:', totalStudents);
        }

        // Get student classes
        function getStudentClasses(student) {
            const classes = [];
            
            if (student.allowedClasses && Array.isArray(student.allowedClasses)) {
                classes.push(...student.allowedClasses);
            }
            
            if (classes.length === 0 && student.courseClass) {
                classes.push(student.courseClass);
            }
            
            return [...new Set(classes)];
        }

        // Get class display name
        function getClassName(classId) {
            const classNames = {
                'python-a': 'Lớp A',
                'python-b': 'Lớp B', 
                'python-c': 'Lớp C'
            };
            return classNames[classId] || classId;
        }

        // Update history dropdown
        function updateHistoryDropdown() {
            const historySelect = document.getElementById('historyMonthSelect');
            historySelect.innerHTML = '<option value="">Chọn tháng để xem</option>';

            monthlyReports.forEach(report => {
                const option = document.createElement('option');
                option.value = report.month;
                option.textContent = report.monthName;
                historySelect.appendChild(option);
            });
        }

        // Switch to current mode
        function switchToCurrentMode() {
            currentViewMode = 'current';
            selectedHistoryMonth = null;

            // Update UI
            document.getElementById('currentModeBtn').classList.add('active');
            document.getElementById('historyModeBtn').classList.remove('active');
            document.getElementById('historySelector').style.display = 'none';
            document.getElementById('finalizeMonthBtn').style.display = 'block';
            document.getElementById('monthTitle').innerHTML = `Quản lý học phí: <span id="currentMonthDisplay">${systemSettings.currentMonthName}</span>`;

            // Refresh data
            displayFeeTable();
            updateSummary();
        }

        // Switch to history mode
        function switchToHistoryMode() {
            currentViewMode = 'history';

            // Update UI
            document.getElementById('currentModeBtn').classList.remove('active');
            document.getElementById('historyModeBtn').classList.add('active');
            document.getElementById('historySelector').style.display = 'block';
            document.getElementById('finalizeMonthBtn').style.display = 'none';
            document.getElementById('monthTitle').textContent = 'Xem lịch sử học phí';

            // Clear table until month is selected
            document.getElementById('feesTableContainer').innerHTML = `
                <div class="no-students">
                    <i class="fas fa-history"></i>
                    <h3>Chọn tháng để xem lịch sử</h3>
                    <p>Vui lòng chọn tháng từ dropdown bên trên</p>
                </div>
            `;

            // Clear stats
            document.getElementById('totalStudents').textContent = '0';
            document.getElementById('paidThisMonth').textContent = '0';
            document.getElementById('unpaidThisMonth').textContent = '0';
            document.getElementById('monthlyRevenue').textContent = '0 ₫';
            document.getElementById('totalRevenue').textContent = '0 ₫';
        }

        // View history month
        function viewHistoryMonth(monthKey) {
            if (!monthKey) {
                switchToHistoryMode(); // Reset to empty state
                return;
            }

            selectedHistoryMonth = monthKey;
            const report = monthlyReports.find(r => r.month === monthKey);

            if (!report) {
                showError('Không tìm thấy dữ liệu cho tháng này');
                return;
            }

            // Update title
            document.getElementById('monthTitle').textContent = `Lịch sử: ${report.monthName}`;

            // Display history table
            displayHistoryTable(report);

            // Update stats with history data
            updateHistoryStats(report);
        }

        // Setup event listeners
        function setupEventListeners() {
            document.getElementById('classFilter').addEventListener('change', applyFilters);
            document.getElementById('statusFilter').addEventListener('change', applyFilters);

            // Add finalize month button listener
            document.getElementById('finalizeMonthBtn').addEventListener('click', finalizeMonth);

            // Add mode toggle listeners
            document.getElementById('currentModeBtn').addEventListener('click', switchToCurrentMode);
            document.getElementById('historyModeBtn').addEventListener('click', switchToHistoryMode);

            // Add history month selector listener
            document.getElementById('historyMonthSelect').addEventListener('change', (e) => {
                viewHistoryMonth(e.target.value);
            });
        }

        // Display history table
        function displayHistoryTable(report) {
            const container = document.getElementById('feesTableContainer');

            if (!report.payments || report.payments.length === 0) {
                container.innerHTML = `
                    <div class="no-students">
                        <i class="fas fa-history"></i>
                        <h3>Không có dữ liệu</h3>
                        <p>Tháng này chưa có dữ liệu thanh toán</p>
                    </div>
                `;
                return;
            }

            let tableHTML = `
                <table class="fees-table">
                    <thead>
                        <tr>
                            <th>Học viên</th>
                            <th class="month-header">${report.monthName}</th>
                        </tr>
                    </thead>
                    <tbody>
            `;

            // Group payments by student
            const paymentsByStudent = {};
            report.payments.forEach(payment => {
                paymentsByStudent[payment.studentId] = payment;
            });

            // Display each student
            Object.values(paymentsByStudent).forEach(payment => {
                const isPaid = payment.isPaid;
                const cellClass = isPaid ? 'paid-cell' : 'unpaid-cell';

                tableHTML += `
                    <tr>
                        <td>
                            <div class="student-info">
                                <img src="../../assets/images/avatars/avatar_boy_1.png" alt="Avatar" class="student-avatar">
                                <div class="student-details">
                                    <div class="student-name">${payment.studentName}</div>
                                    <div class="student-class">${getClassName(payment.classId)}</div>
                                </div>
                            </div>
                        </td>
                        <td class="${cellClass}">
                            <input type="checkbox" class="fee-checkbox"
                                   ${isPaid ? 'checked' : ''}
                                   disabled>
                            ${isPaid ? '<small style="color: #27ae60; margin-left: 8px;">Đã đóng</small>' : '<small style="color: #e74c3c; margin-left: 8px;">Chưa đóng</small>'}
                        </td>
                    </tr>
                `;
            });

            tableHTML += `
                    </tbody>
                </table>
            `;

            container.innerHTML = tableHTML;
        }

        // Update history stats
        function updateHistoryStats(report) {
            document.getElementById('totalStudents').textContent = report.totalStudents;
            document.getElementById('paidThisMonth').textContent = report.paidStudents;
            document.getElementById('unpaidThisMonth').textContent = report.unpaidStudents;
            document.getElementById('monthlyRevenue').textContent = formatCurrency(report.totalRevenue);

            // Calculate total revenue from all reports
            const totalAllTime = monthlyReports.reduce((sum, r) => sum + (r.totalRevenue || 0), 0);
            document.getElementById('totalRevenue').textContent = formatCurrency(totalAllTime);
        }

        // Apply filters
        function applyFilters() {
            if (currentViewMode === 'current') {
                displayFeeTable();
                updateSummary();
            } else if (currentViewMode === 'history' && selectedHistoryMonth) {
                const report = monthlyReports.find(r => r.month === selectedHistoryMonth);
                if (report) {
                    displayHistoryTable(report);
                    updateHistoryStats(report);
                }
            }
        }

        // Finalize current month and move to next month
        async function finalizeMonth() {
            const currentMonth = getCurrentMonth();
            const nextMonth = getNextMonth();

            if (!confirm(`Bạn có chắc chắn muốn kết toán ${currentMonth.name}?\n\nSau khi kết toán, dữ liệu tháng này sẽ được lưu vào lịch sử và hệ thống sẽ chuyển sang ${nextMonth.name}.`)) {
                return;
            }

            try {
                // 1. Calculate current month statistics
                const currentMonthPayments = feePayments.filter(p => p.month === currentMonth.key);
                const totalStudents = studentsData.length;
                const paidStudents = currentMonthPayments.filter(p => p.isPaid).length;
                const unpaidStudents = totalStudents - paidStudents;
                const totalRevenue = paidStudents * 250000;

                // 2. Create monthly report
                const monthlyReport = {
                    month: currentMonth.key,
                    monthName: currentMonth.name,
                    totalStudents: totalStudents,
                    paidStudents: paidStudents,
                    unpaidStudents: unpaidStudents,
                    totalRevenue: totalRevenue,
                    finalizedAt: new Date().toISOString(),
                    payments: currentMonthPayments.map(p => ({...p})) // Deep copy
                };

                // 3. Save monthly report
                await saveMonthlyReport(monthlyReport);

                // 4. Update system settings to next month
                systemSettings.currentMonth = nextMonth.key;
                systemSettings.currentMonthName = nextMonth.name;
                await saveSystemSettings();

                // 5. Clear current month payments (they're now in the report)
                const batch = writeBatch(db);
                currentMonthPayments.forEach(payment => {
                    batch.delete(doc(db, "feePayments", payment.id));
                });
                await batch.commit();

                // 6. Reload data and refresh UI
                feePayments = []; // Clear local data
                await initializeFeePayments(); // Create payments for new month

                // Update UI
                document.getElementById('currentMonthDisplay').textContent = nextMonth.name;
                displayFeeTable();
                updateSummary();

                showSuccess(`Đã kết toán ${currentMonth.name} thành công! Chuyển sang ${nextMonth.name}.`);

            } catch (error) {
                console.error('Error finalizing month:', error);
                showError('Lỗi khi kết toán tháng: ' + error.message);
            }
        }

        // Utility functions
        function formatCurrency(amount) {
            return new Intl.NumberFormat('vi-VN', {
                style: 'currency',
                currency: 'VND'
            }).format(amount);
        }

        function showError(message) {
            const errorDiv = document.createElement('div');
            errorDiv.className = 'error-message';
            errorDiv.textContent = message;
            
            const container = document.querySelector('.container');
            container.insertBefore(errorDiv, container.firstChild);
            
            setTimeout(() => {
                errorDiv.remove();
            }, 5000);
        }

        function showSuccess(message) {
            const successDiv = document.createElement('div');
            successDiv.className = 'success-message';
            successDiv.textContent = message;
            
            const container = document.querySelector('.container');
            container.insertBefore(successDiv, container.firstChild);
            
            setTimeout(() => {
                successDiv.remove();
            }, 5000);
        }
    </script>
</body>
</html>
