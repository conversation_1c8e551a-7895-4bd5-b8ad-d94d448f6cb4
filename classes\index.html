<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Lớp <PERSON> - <PERSON>thon</title>
    <link rel="stylesheet" href="../assets/css/styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        .page-header {
            text-align: center;
            margin: 120px 0 50px;
        }
        
        .page-header h1 {
            font-size: 2.5rem;
            color: #333;
            margin-bottom: 15px;
        }
        
        .page-header p {
            color: #666;
            font-size: 1.2rem;
            max-width: 700px;
            margin: 0 auto;
        }
        
        .classes-container {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 30px;
            padding: 20px 0 50px;
        }
        
        .class-card {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            width: 350px;
            transition: transform 0.3s, box-shadow 0.3s;
        }
        
        .class-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        }
        
        .class-image {
            height: 180px;
            overflow: hidden;
        }
        
        .class-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .class-details {
            padding: 20px;
        }
        
        .class-name {
            font-size: 1.5rem;
            color: #333;
            margin-bottom: 10px;
            font-weight: 700;
        }
        
        .class-schedule {
            margin-bottom: 15px;
        }
        
        .schedule-item {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
            color: #555;
        }
        
        .schedule-item i {
            color: #4285F4;
            margin-right: 10px;
            font-size: 1.1rem;
        }
        
        .students-section {
            margin-top: 20px;
            border-top: 1px solid #eee;
            padding-top: 15px;
        }
        
        .students-count {
            font-weight: 500;
            color: #333;
            margin-bottom: 10px;
        }
        
        .students-avatars {
            display: flex;
            flex-wrap: wrap;
            gap: 5px;
        }
        
        .student-avatar {
            width: 35px;
            height: 35px;
            border-radius: 50%;
            overflow: hidden;
            border: 2px solid #fff;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .student-avatar:hover {
            transform: scale(1.1);
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
        }
        
        .student-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .empty-class {
            background-color: #f8f9fa;
            border: 2px dashed #dee2e6;
        }
        
        .empty-notice {
            color: #6c757d;
            font-style: italic;
            margin-top: 5px;
        }
        
        .join-class {
            display: inline-block;
            background-color: #4285F4;
            color: white;
            padding: 10px 20px;
            border-radius: 5px;
            text-decoration: none;
            margin-top: 15px;
            font-weight: 500;
            transition: background-color 0.3s;
        }
        
        .join-class:hover {
            background-color: #3367D6;
        }

        .join-class.disabled {
            background-color: #6c757d;
            cursor: not-allowed;
            opacity: 0.6;
        }

        .join-class.disabled:hover {
            background-color: #6c757d;
        }

        .access-denied {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
            border-radius: 5px;
            padding: 10px;
            margin-top: 10px;
            font-size: 0.9rem;
        }

        .loading {
            text-align: center;
            padding: 50px;
            color: #666;
        }

        .error-message {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
            text-align: center;
        }
    </style>
</head>
<body>
    <!-- Navigation Bar -->
    <header>
        <div class="container">
            <div class="logo">
                <img src="../assets/images/logo.jpg" alt="VTA Logo">
            </div>
            <nav>
                <ul>
                    <li><a href="../index.html">Trang Chủ</a></li>
                    <li><a href="index.html" class="active">Lớp Học</a></li>
                    <li><a href="../achievements/">Thành Tích</a></li>
                    <li><a href="../auth/register.html">Đăng Ký</a></li>
                    <li><a href="../rankings/">Bảng Xếp Hạng</a></li>
                    <li><a href="../research/">Nghiên Cứu</a></li>
                    <li><a href="../auth/">Tài Khoản</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <!-- Classes Section -->
    <section>
        <div class="container">
            <div class="page-header">
                <h1>Các Lớp Học Hiện Tại</h1>
                <p>Khám phá và tham gia các lớp học Python và AI cùng Vthon</p>
            </div>

            <!-- Loading State -->
            <div id="loadingState" class="loading">
                <i class="fas fa-spinner fa-spin"></i> Đang tải thông tin lớp học...
            </div>

            <!-- Error Message -->
            <div id="errorMessage" class="error-message" style="display: none;"></div>

            <div id="classesContainer" class="classes-container" style="display: none;">
                <!-- Classes will be loaded dynamically -->
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-section footer-logo">
                    <div class="footer-logo-container">
                        <img src="../assets/images/logo.jpg" alt="VTA Logo">
                        <div class="logo-text">Vthon Academy</div>
                    </div>
                    <div class="slogan">Học, học nữa, học mãi.</div>
                    <div class="footer-bottom">
                        <p>&copy; 2025 – All rights reserved.</p>
                    </div>
                </div>

                <div class="footer-section footer-contact">
                    <h3>Liên hệ</h3>
                    <p><i class="fas fa-envelope"></i> <EMAIL></p>
                    <p><i class="fas fa-phone"></i> 0399787678</p>
                    <p><i class="fab fa-facebook-messenger"></i> Zalo: 0399787678</p>
                </div>

                <div class="footer-section footer-social">
                    <h3>Mạng xã hội</h3>
                    <div class="social-links">
                        <a href="https://www.facebook.com/vinhle030904/" target="_blank" class="social-link facebook">
                            <i class="fab fa-facebook-f"></i> Facebook
                        </a>
                        <a href="https://www.tiktok.com/@sunnii39" target="_blank" class="social-link tiktok">
                            <i class="fab fa-tiktok"></i> TikTok
                        </a>
                        <a href="https://www.instagram.com/vlee.39/?hl=en" target="_blank" class="social-link instagram">
                            <i class="fab fa-instagram"></i> Instagram
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <script type="module">
        // Import Firebase modules
        import { initializeApp } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-app.js";
        import { getAuth, onAuthStateChanged } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-auth.js";
        import { getFirestore, doc, getDoc, collection, query, where, getDocs } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-firestore.js";

        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyBKNo8y_MOKYc3f3UdNRFwcMgeLRW71WXA",
            authDomain: "classroom-web-48bc2.firebaseapp.com",
            projectId: "classroom-web-48bc2",
            storageBucket: "classroom-web-48bc2.firebasestorage.app",
            messagingSenderId: "446746787502",
            appId: "1:446746787502:web:48d5ffd5a0b2c6e043b73f",
            measurementId: "G-742XRP9E96"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const auth = getAuth(app);
        const db = getFirestore(app);

        // UI Elements
        const loadingState = document.getElementById('loadingState');
        const errorMessage = document.getElementById('errorMessage');
        const classesContainer = document.getElementById('classesContainer');

        // Classes data
        const classesData = [
            {
                id: 'python-a',
                name: 'Python - A',
                schedule: 'Thứ 7 - Chủ Nhật',
                time: '19:30 - 21:00',
                students: 0, // Will be calculated from Firebase
                maxStudents: 10
            },
            {
                id: 'python-b',
                name: 'Python - B',
                schedule: 'Thứ 2 - Thứ 4',
                time: '19:30 - 21:00',
                students: 0, // Will be calculated from Firebase
                maxStudents: 10
            },
            {
                id: 'python-c',
                name: 'Python - C',
                schedule: 'Thứ 3 - Thứ 5',
                time: '19:30 - 21:00',
                students: 0, // Will be calculated from Firebase
                maxStudents: 10
            }
        ];

        // Get students count for a specific class from Firebase
        async function getStudentsCount(classId) {
            try {
                console.log(`Getting students count for class: ${classId}`);

                const q = query(
                    collection(db, "users"),
                    where("courseClass", "==", classId)
                );
                const querySnapshot = await getDocs(q);
                console.log(`Found ${querySnapshot.size} users for class ${classId}`);

                // Filter users who have fullName (profile completed)
                let count = 0;
                querySnapshot.forEach((doc) => {
                    const userData = doc.data();
                    console.log(`User data:`, userData);
                    if (userData.fullName && userData.fullName.trim() !== '') {
                        count++;
                    }
                });

                console.log(`Final count for class ${classId}: ${count}`);
                return count;
            } catch (error) {
                console.error("Error getting students count:", error);
                return 0; // Return 0 on error
            }
        }

        // Get students data for a specific class from Firebase
        async function getStudentsData(classId, limit = 5) {
            try {
                console.log(`Getting students data for class: ${classId}`);
                const q = query(
                    collection(db, "users"),
                    where("courseClass", "==", classId)
                );
                const querySnapshot = await getDocs(q);
                const students = [];

                querySnapshot.forEach((doc) => {
                    const userData = doc.data();
                    console.log(`Processing user:`, userData);
                    // Only include users who have completed their profile
                    if (userData.fullName && userData.fullName.trim() !== '') {
                        students.push({
                            id: doc.id,
                            userId: doc.id,
                            name: userData.fullName,
                            avatar: userData.avatar || '../assets/images/avatars/avatar_boy_1.png'
                        });
                    }
                });

                console.log(`Students found for class ${classId}:`, students);
                return students.slice(0, limit); // Return only first 5 students
            } catch (error) {
                console.error("Error getting students data:", error);
                return []; // Return empty array on error
            }
        }

        // Check if user has access to a class
        async function checkClassAccess(user, classId) {
            if (!user) return false;

            try {
                const userDoc = await getDoc(doc(db, "users", user.uid));
                if (userDoc.exists()) {
                    const userData = userDoc.data();

                    // Check if user is admin
                    const isAdmin = userData.isAdmin || user.email === '<EMAIL>';

                    // Admin has access to all classes, or user has access to their selected class
                    return isAdmin || userData.courseClass === classId;
                }
                return false;
            } catch (error) {
                console.error("Error checking class access:", error);
                return false;
            }
        }

        // Create class card HTML
        async function createClassCard(classData, hasAccess, user) {
            // Get real students data from Firebase
            const studentsData = await getStudentsData(classData.id, 5);
            const studentsCount = await getStudentsCount(classData.id);

            const isEmpty = studentsCount === 0;
            const cardClass = isEmpty ? 'class-card empty-class' : 'class-card';

            let studentsHTML = '';
            if (studentsCount > 0 && studentsData.length > 0) {
                studentsHTML = '<div class="students-avatars">';
                studentsData.forEach(student => {
                    studentsHTML += `
                        <div class="student-avatar" title="${student.name}" onclick="viewStudentProfile('${student.userId}')" style="cursor: pointer;">
                            <img src="${student.avatar}" alt="${student.name}">
                        </div>
                    `;
                });
                studentsHTML += '</div>';
            } else {
                studentsHTML = '<p class="empty-notice">Lớp học chưa có học viên</p>';
            }

            let actionHTML = '';
            if (!user) {
                actionHTML = `<a href="../auth/" class="join-class">Đăng nhập để tham gia lớp học</a>`;
            } else if (hasAccess) {
                actionHTML = `<a href="${classData.id}.html" class="join-class">Vào lớp học</a>`;
            } else {
                actionHTML = `
                    <a href="#" class="join-class disabled" onclick="showAccessDenied(); return false;">Tham gia lớp học</a>
                    <div class="access-denied">
                        <i class="fas fa-lock"></i>
                        Bạn hiện không có quyền truy cập lớp học này!
                    </div>
                `;
            }

            return `
                <div class="${cardClass}">
                    <div class="class-image">
                        <img src="../assets/images/classavatar.png" alt="${classData.name}">
                    </div>
                    <div class="class-details">
                        <h3 class="class-name">${classData.name}</h3>
                        <div class="class-schedule">
                            <div class="schedule-item">
                                <i class="fas fa-calendar-alt"></i>
                                <span>${classData.schedule}</span>
                            </div>
                            <div class="schedule-item">
                                <i class="fas fa-clock"></i>
                                <span>${classData.time}</span>
                            </div>
                        </div>
                        <div class="students-section">
                            <div class="students-count">Số học viên: ${studentsCount}/${classData.maxStudents}</div>
                            ${studentsHTML}
                        </div>
                        ${actionHTML}
                    </div>
                </div>
            `;
        }

        // Show access denied message
        window.showAccessDenied = function() {
            alert('Bạn hiện không có quyền truy cập lớp học này!\n\nVui lòng liên hệ giáo viên để được phân lớp hoặc kiểm tra lại thông tin lớp học đã chọn trong tài khoản.');
        };

        // Load classes
        async function loadClasses(user) {
            try {
                loadingState.style.display = 'block';
                errorMessage.style.display = 'none';
                classesContainer.style.display = 'none';

                let classesHTML = '';

                for (const classData of classesData) {
                    const hasAccess = user ? await checkClassAccess(user, classData.id) : false;
                    classesHTML += await createClassCard(classData, hasAccess, user);
                }

                classesContainer.innerHTML = classesHTML;

                loadingState.style.display = 'none';
                classesContainer.style.display = 'flex';
            } catch (error) {
                console.error("Error loading classes:", error);
                loadingState.style.display = 'none';
                errorMessage.textContent = 'Lỗi khi tải thông tin lớp học: ' + error.message;
                errorMessage.style.display = 'block';
            }
        }

        let authStateChecked = false;

        // View student profile (make it global)
        window.viewStudentProfile = function(userId) {
            // Redirect to student profile page with userId parameter
            window.location.href = `../auth/student-profile.html?userId=${userId}`;
        };

        // Auth state change listener
        onAuthStateChanged(auth, (user) => {
            if (!authStateChecked) {
                authStateChecked = true;
                loadClasses(user);
            } else {
                // Only reload if auth state actually changed
                loadClasses(user);
            }
        });
    </script>

    <script src="../assets/js/script.js"></script>
</body>
</html>