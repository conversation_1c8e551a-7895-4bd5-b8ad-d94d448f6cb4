<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Python - A | Classroom Web</title>
    <link rel="stylesheet" href="../assets/css/styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .class-header {
            background: linear-gradient(135deg, #FF6B35 0%, #F7931E 50%, #FFD23F 100%);
            color: white;
            padding: 60px 0;
            text-align: center;
            margin-top: 80px; /* Add margin to avoid navigation overlap */
            box-shadow: 0 4px 20px rgba(255, 107, 53, 0.3);
        }

        .class-header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            word-wrap: break-word;
        }

        .class-header p {
            font-size: 1.1rem;
            opacity: 0.9;
            word-wrap: break-word;
        }

        @media (max-width: 768px) {
            .class-header h1 {
                font-size: 2rem;
            }
            .class-header p {
                font-size: 1rem;
            }
        }

        .class-info {
            background: white;
            padding: 40px 0;
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }

        .info-card {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .info-card i {
            font-size: 2.5rem;
            color: #FF8C00;
            margin-bottom: 15px;
        }

        .info-card h3 {
            color: #333;
            margin-bottom: 10px;
        }

        .access-denied {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            margin: 20px 0;
        }

        .access-granted {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            margin: 20px 0;
        }

        .lessons-section {
            background: #f8f9fa;
            padding: 40px 0;
        }

        .lessons-navigation {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }

        .lesson-tab {
            background: white;
            border: 2px solid #FF8C00;
            color: #FF8C00;
            padding: 12px 25px;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
            min-width: 120px;
            text-align: center;
        }

        .lesson-tab:hover {
            background: #FF8C00;
            color: white;
            transform: translateY(-2px);
        }

        .lesson-tab.active {
            background: #FF8C00;
            color: white;
            box-shadow: 0 4px 15px rgba(255, 140, 0, 0.3);
        }

        .lesson-content {
            display: none;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            animation: fadeIn 0.3s ease-in-out;
        }

        .lesson-content.active {
            display: block;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .lesson-content h3 {
            color: #FF8C00;
            margin-bottom: 20px;
            font-size: 1.5rem;
        }

        .lesson-objectives {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin: 15px 0;
        }

        .lesson-objectives h4 {
            color: #333;
            margin-bottom: 10px;
        }

        .lesson-objectives ul {
            margin: 10px 0;
            padding-left: 20px;
        }

        .lesson-objectives li {
            margin-bottom: 8px;
            line-height: 1.5;
        }

        .lesson-details {
            margin: 20px 0;
        }

        .lesson-details h4 {
            color: #FF8C00;
            margin: 15px 0 10px 0;
        }

        .lesson-status {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 25px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
        }

        .status-info {
            flex: 1;
        }

        .assignment-button {
            margin-left: 20px;
        }

        .meet-link {
            display: inline-block;
            background: linear-gradient(135deg, #FFD700, #FF8C00);
            color: white;
            padding: 12px 25px;
            border-radius: 25px;
            text-decoration: none;
            margin-top: 15px;
            transition: transform 0.3s ease;
        }

        .meet-link:hover {
            transform: translateY(-2px);
        }

        .students-display {
            margin-top: 20px;
        }

        .student-avatars {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 10px;
        }

        .student-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: linear-gradient(135deg, #FFD700, #FF8C00);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 18px;
            position: relative;
            cursor: pointer;
        }

        .student-avatar:hover::after {
            content: attr(data-name);
            position: absolute;
            bottom: -30px;
            left: 50%;
            transform: translateX(-50%);
            background: #333;
            color: white;
            padding: 5px 10px;
            border-radius: 5px;
            font-size: 12px;
            white-space: nowrap;
            z-index: 1000;
        }

        .student-avatar img {
            width: 100%;
            height: 100%;
            border-radius: 50%;
            object-fit: cover;
        }
    </style>
</head>
<body>
    <!-- Navigation Bar -->
    <header>
        <div class="container">
            <div class="logo">
                <img src="../assets/images/logo.jpg" alt="VTA Logo">
            </div>
            <nav>
                <ul>
                    <li><a href="../index.html">Trang Chủ</a></li>
                    <li><a href="index.html">Lớp Học</a></li>
                    <li><a href="../achievements/">Thành Tích</a></li>
                    <li><a href="../auth/register.html">Đăng Ký</a></li>
                    <li><a href="../rankings/">Bảng Xếp Hạng</a></li>
                    <li><a href="../research/">Nghiên Cứu</a></li>
                    <li><a href="../auth/">Tài Khoản</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <!-- Class Header -->
    <section class="class-header">
        <div class="container">
            <h1><i class="fab fa-python"></i> Python - A</h1>
            <p>Lớp học Python cơ bản đến nâng cao - Thứ 7 & Chủ Nhật</p>
        </div>
    </section>

    <!-- Access Control Message -->
    <section class="class-info">
        <div class="container">
            <div id="accessMessage" style="display: none;"></div>
            
            <div id="classContent" style="display: none;">
                <!-- Class Information -->
                <div class="info-grid">
                    <div class="info-card">
                        <i class="fas fa-calendar-alt"></i>
                        <h3>Lịch Học</h3>
                        <p>Thứ 7 - Chủ Nhật<br>19:30 - 21:00</p>
                    </div>
                    <div class="info-card">
                        <i class="fas fa-users"></i>
                        <h3>Học Viên</h3>
                        <p id="studentCount">Đang tải...</p>
                        <div class="students-display">
                            <div id="studentAvatars" class="student-avatars"></div>
                        </div>
                    </div>
                    <div class="info-card">
                        <i class="fas fa-video"></i>
                        <h3>Google Meet</h3>
                        <a href="https://meet.google.com/ysi-jixy-qms" target="_blank" class="meet-link">
                            <i class="fas fa-video"></i> Tham gia lớp học
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Lessons Section -->
    <section class="lessons-section" id="lessonsSection" style="display: none;">
        <div class="container">
            <h2 style="text-align: center; margin-bottom: 40px;">Nội Dung Bài Học</h2>

            <!-- Lessons Navigation -->
            <div class="lessons-navigation">
                <div class="lesson-tab active" onclick="showLesson(1)">
                    <i class="fas fa-play-circle"></i> Bài 1
                </div>
                <div class="lesson-tab" onclick="showLesson(2)">
                    <i class="fas fa-play-circle"></i> Bài 2
                </div>
                <div class="lesson-tab" onclick="showLesson(3)">
                    <i class="fas fa-play-circle"></i> Bài 3
                </div>
            </div>

            <!-- Lesson 1 Content -->
            <div class="lesson-content active" id="lesson1">
                <h3><i class="fas fa-play-circle"></i> Bài 1: Chào Mừng Đến Với Kỷ Nguyên Số và Thế Giới Lập Trình</h3>

                <div class="lesson-objectives">
                    <h4><i class="fas fa-target"></i> Mục tiêu:</h4>
                    <ul>
                        <li>Hiểu được khái niệm Công nghệ Thông tin (CNTT) và vai trò của nó trong đời sống.</li>
                        <li>Biết được một số lĩnh vực chính và cơ hội nghề nghiệp trong ngành CNTT.</li>
                        <li>Hiểu được Lập trình là gì và tại sao nên học lập trình.</li>
                    </ul>
                </div>

                <div class="lesson-details">
                    <h4><i class="fas fa-book"></i> Nội dung chính:</h4>
                    <div style="margin-left: 20px;">
                        <p><strong>1. Tổng quan về Công nghệ Thông tin (CNTT):</strong></p>
                        <ul>
                            <li>CNTT là gì? Ví dụ thực tiễn (smartphone, Internet, mạng xã hội, game, ứng dụng học tập).</li>
                            <li>Tầm quan trọng và ảnh hưởng của CNTT tới xã hội hiện đại.</li>
                            <li>Các lĩnh vực chính trong CNTT (sơ lược): Phát triển phần mềm, Khoa học dữ liệu, An ninh mạng, Trí tuệ nhân tạo, v.v.</li>
                        </ul>
                        <p><strong>2. Giới thiệu về Lập trình:</strong></p>
                        <ul>
                            <li>Lập trình là gì? (Cách con người "nói chuyện" và "ra lệnh" cho máy tính).</li>
                            <li>Lợi ích của việc học lập trình: Phát triển tư duy logic, giải quyết vấn đề, sáng tạo.</li>
                            <li>Ngôn ngữ lập trình là gì? Giới thiệu sơ qua về sự đa dạng của các ngôn ngữ.</li>
                        </ul>
                    </div>
                    <p><strong><i class="fas fa-clock"></i> Thời lượng dự kiến:</strong> 1 buổi</p>
                    <p><strong><i class="fas fa-file-alt"></i> Tài liệu:</strong> <a href="https://www.canva.com/design/DAGoEIUCxeE/f4G5xyktcg_yVi0fGpJlFw/edit?utm_content=DAGoEIUCxeE&utm_campaign=designshare&utm_medium=link2&utm_source=sharebutton" target="_blank" style="color: #FF8C00;">Xem tài liệu Canva</a></p>
                </div>

                <div class="lesson-status">
                    <div class="status-info">
                        <p><strong><i class="fas fa-chart-line"></i> Trạng thái:</strong> <span id="assignmentStatus" style="color: #28a745;">Đang kiểm tra...</span></p>
                    </div>
                    <div class="assignment-button">
                        <a href="assignments/python-a/assignment-1.html" class="meet-link" id="assignmentLink">
                            <i class="fas fa-tasks"></i> <span id="assignmentLinkText">Làm bài tập</span>
                        </a>
                    </div>
                </div>
            </div>

            <!-- Lesson 2 Content -->
            <div class="lesson-content" id="lesson2">
                <h3><i class="fas fa-play-circle"></i> Bài 2: Python – Ngôn Ngữ Của Sự Sáng Tạo và Cài Đặt Môi Trường</h3>

                <div class="lesson-objectives">
                    <h4><i class="fas fa-target"></i> Mục tiêu:</h4>
                    <ul>
                        <li>Biết được Python là gì, lịch sử và ưu điểm của Python.</li>
                        <li>Cài đặt thành công Python và Visual Studio Code (VS Code).</li>
                        <li>Làm quen với giao diện VS Code và chạy được chương trình Python đầu tiên.</li>
                        <li>Biết đến Google Colab như một công cụ thay thế/bổ trợ.</li>
                    </ul>
                </div>

                <div class="lesson-details">
                    <h4><i class="fas fa-book"></i> Nội dung chính:</h4>
                    <div style="margin-left: 20px;">
                        <p><strong>1. Giới thiệu Ngôn ngữ Lập trình Python:</strong></p>
                        <ul>
                            <li>Python là gì? Lịch sử phát triển (ngắn gọn).</li>
                            <li>Tại sao chọn Python? (Dễ học, dễ đọc, cú pháp rõ ràng, cộng đồng lớn, ứng dụng rộng rãi, đặc biệt trong AI và Khoa học Dữ liệu).</li>
                        </ul>
                        <p><strong>2. Cài đặt Môi trường Lập trình (Dạy Online):</strong></p>
                        <ul>
                            <li>Hướng dẫn chi tiết cách tải và cài đặt Python từ trang chủ python.org.</li>
                            <li>Hướng dẫn chi tiết cách tải, cài đặt và cấu hình Visual Studio Code (VS Code) cho lập trình Python (cài đặt extension Python).</li>
                            <li>Tạo thư mục dự án, tạo file Python (.py).</li>
                        </ul>
                        <p><strong>3. Chương trình Python Đầu tiên:</strong></p>
                        <ul>
                            <li>Viết và chạy chương trình "Hello, World!" trong VS Code.</li>
                            <li>Giải thích ý nghĩa cơ bản của chương trình.</li>
                            <li>Khái niệm về comments (ghi chú) trong code.</li>
                        </ul>
                        <p><strong>4. Giới thiệu Google Colaboratory (Google Colab):</strong></p>
                        <ul>
                            <li>Google Colab là gì? Lợi ích (không cần cài đặt, có sẵn thư viện, dễ chia sẻ, GPU miễn phí cho AI).</li>
                            <li>Hướng dẫn truy cập và tạo một notebook đơn giản trên Google Colab, chạy lệnh "Hello, World!".</li>
                            <li>Lưu ý: VS Code sẽ là môi trường chính, Colab là công cụ tham khảo và sử dụng khi cần thiết.</li>
                        </ul>
                    </div>
                    <p><strong><i class="fas fa-clock"></i> Thời lượng dự kiến:</strong> 1 buổi</p>
                    <p><strong><i class="fas fa-file-alt"></i> Tài liệu:</strong> <a href="https://www.canva.com/design/DAGpv2-6nwU/sUkdmc922qoeBBabtOR5JA/edit?utm_content=DAGpv2-6nwU&utm_campaign=designshare&utm_medium=link2&utm_source=sharebutton" target="_blank" style="color: #FF8C00;">Xem tài liệu Canva</a></p>
                </div>

                <div class="lesson-status">
                    <div class="status-info">
                        <p><strong><i class="fas fa-chart-line"></i> Trạng thái:</strong> <span id="assignment2Status" style="color: #28a745;">Đang kiểm tra...</span></p>
                    </div>
                    <div class="assignment-buttons" style="display: flex; gap: 15px; flex-wrap: wrap;">
                        <div class="assignment-button" style="flex: 1; min-width: 200px;">
                            <a href="assignments/python-a/assignment-2.html" class="meet-link" id="quizAssignmentLink" style="background: linear-gradient(135deg, #4285F4, #6fa8f5);">
                                <i class="fas fa-question-circle"></i> <span id="quizAssignmentText">Làm trắc nghiệm</span>
                            </a>
                            <div class="assignment-status" id="quizAssignmentStatus" style="text-align: center; margin-top: 8px; font-size: 0.9em; color: #666;">
                                Đang kiểm tra...
                            </div>
                        </div>
                        <div class="assignment-button" style="flex: 1; min-width: 200px;">
                            <a href="assignments/python-a/coding-assignment-2.html" class="meet-link" id="codingAssignmentLink" style="background: linear-gradient(135deg, #28a745, #5cbf2a);">
                                <i class="fas fa-code"></i> <span id="codingAssignmentText">Thực hành Code</span>
                            </a>
                            <div class="assignment-status" id="codingAssignmentStatus" style="text-align: center; margin-top: 8px; font-size: 0.9em; color: #666;">
                                Đang kiểm tra...
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Lesson 3 Content -->
            <div class="lesson-content" id="lesson3">
                <h3><i class="fas fa-play-circle"></i> Bài 3: Dữ Liệu – Biến, Kiểu Số và Chuỗi</h3>

                <div class="lesson-objectives">
                    <h4><i class="fas fa-target"></i> Mục tiêu:</h4>
                    <ul>
                        <li>Hiểu và sử dụng được biến (variables)</li>
                        <li>Nắm được các kiểu dữ liệu cơ bản: số nguyên (integer), số thực (float), chuỗi (string)</li>
                        <li>Thực hiện được các thao tác cơ bản với chuỗi</li>
                    </ul>
                </div>

                <div class="lesson-details">
                    <h4><i class="fas fa-book"></i> Nội dung chính:</h4>
                    <div style="margin-left: 20px;">
                        <p><strong>1. Biến (Variables):</strong></p>
                        <ul>
                            <li>Khái niệm: Biến là "hộp chứa" để lưu trữ dữ liệu trong chương trình</li>
                            <li>Quy tắc đặt tên biến: Bắt đầu bằng chữ cái hoặc dấu gạch dưới (_), không chứa khoảng trắng</li>
                            <li>Phép gán: Sử dụng dấu = để gán giá trị cho biến</li>
                        </ul>
                        <p><strong>2. Kiểu dữ liệu Số (Numbers):</strong></p>
                        <ul>
                            <li>Số nguyên (integer): Các số không có phần thập phân</li>
                            <li>Số thực (float): Các số có phần thập phân</li>
                        </ul>
                        <p><strong>3. Kiểu dữ liệu Chuỗi (String):</strong></p>
                        <ul>
                            <li>Chuỗi là dãy các ký tự được bao quanh bởi dấu nháy</li>
                            <li>Các thao tác: nối chuỗi (+), lặp chuỗi (*), độ dài (len), truy cập ký tự</li>
                            <li>Phương thức: upper(), lower(), strip(), find(), replace()</li>
                        </ul>
                    </div>
                    <p><strong><i class="fas fa-clock"></i> Thời lượng dự kiến:</strong> 1 buổi</p>
                    <p><strong><i class="fas fa-file-alt"></i> Tài liệu:</strong> <a href="https://www.canva.com/design/DAGqGqjOQdg/Skr50HWmwxHBIjNrgkUCtg/edit?utm_content=DAGqGqjOQdg&utm_campaign=designshare&utm_medium=link2&utm_source=sharebutton" target="_blank" style="color: #FF8C00;">Xem tài liệu Canva</a></p>
                </div>

                <div class="lesson-status">
                    <div class="status-info">
                        <p><strong><i class="fas fa-chart-line"></i> Trạng thái:</strong> <span id="assignment3Status" style="color: #28a745;">Đang kiểm tra...</span></p>
                    </div>
                    <div class="assignment-buttons" style="display: flex; gap: 15px; flex-wrap: wrap;">
                        <div class="assignment-button" style="flex: 1; min-width: 200px;">
                            <a href="assignments/python-a/lesson-3-quiz.html" class="meet-link" id="lesson3QuizLink" style="background: linear-gradient(135deg, #667eea, #764ba2);">
                                <i class="fas fa-question-circle"></i> <span id="lesson3QuizText">Làm trắc nghiệm</span>
                            </a>
                            <div class="assignment-status" id="lesson3QuizStatus" style="text-align: center; margin-top: 8px; font-size: 0.9em; color: #666;">
                                Đang kiểm tra...
                            </div>
                        </div>
                        <div class="assignment-button" style="flex: 1; min-width: 200px;">
                            <a href="assignments/python-a/lesson-3-code.html" class="meet-link" id="lesson3CodeLink" style="background: linear-gradient(135deg, #28a745, #20c997);">
                                <i class="fas fa-code"></i> <span id="lesson3CodeText">Luyện tập Code</span>
                            </a>
                            <div class="assignment-status" id="lesson3CodeStatus" style="text-align: center; margin-top: 8px; font-size: 0.9em; color: #666;">
                                Bài tập luyện tập (không tính điểm)
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-section footer-logo">
                    <div class="footer-logo-container">
                        <img src="../assets/images/logo.jpg" alt="VTA Logo">
                        <div class="logo-text">Vthon Academy</div>
                    </div>
                    <div class="slogan">Học, học nữa, học mãi.</div>
                    <div class="footer-bottom">
                        <p>&copy; 2025 – All rights reserved.</p>
                    </div>
                </div>

                <div class="footer-section footer-contact">
                    <h3>Liên hệ</h3>
                    <p><i class="fas fa-envelope"></i> <EMAIL></p>
                    <p><i class="fas fa-phone"></i> 0399787678</p>
                    <p><i class="fab fa-facebook-messenger"></i> Zalo: 0399787678</p>
                </div>

                <div class="footer-section footer-social">
                    <h3>Mạng xã hội</h3>
                    <div class="social-links">
                        <a href="https://www.facebook.com/vinhle030904/" target="_blank" class="social-link facebook">
                            <i class="fab fa-facebook-f"></i> Facebook
                        </a>
                        <a href="https://www.tiktok.com/@sunnii39" target="_blank" class="social-link tiktok">
                            <i class="fab fa-tiktok"></i> TikTok
                        </a>
                        <a href="https://www.instagram.com/vlee.39/?hl=en" target="_blank" class="social-link instagram">
                            <i class="fab fa-instagram"></i> Instagram
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Firebase SDK -->
    <script type="module">
        import { initializeApp } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-app.js";
        import { getAuth, onAuthStateChanged } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-auth.js";
        import { getFirestore, doc, getDoc, collection, getDocs, query, where } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-firestore.js";

        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyBKNo8y_MOKYc3f3UdNRFwcMgeLRW71WXA",
            authDomain: "classroom-web-48bc2.firebaseapp.com",
            projectId: "classroom-web-48bc2",
            storageBucket: "classroom-web-48bc2.firebasestorage.app",
            messagingSenderId: "446746787502",
            appId: "1:446746787502:web:48d5ffd5a0b2c6e043b73f",
            measurementId: "G-742XRP9E96"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const auth = getAuth(app);
        const db = getFirestore(app);

        // Check access permissions
        onAuthStateChanged(auth, async (user) => {
            const accessMessage = document.getElementById('accessMessage');
            const classContent = document.getElementById('classContent');
            const lessonsSection = document.getElementById('lessonsSection');

            if (user) {
                try {
                    const userDoc = await getDoc(doc(db, "users", user.uid));
                    const userData = userDoc.data();

                    // Check if user is admin
                    const isAdmin = userData && (userData.isAdmin || user.email === '<EMAIL>');
                    
                    // Check if user has access to Python-A class
                    const hasAccess = isAdmin || (userData && userData.courseClass === 'python-a');

                    if (hasAccess) {
                        // Grant access
                        accessMessage.innerHTML = `
                            <div class="access-granted">
                                <i class="fas fa-check-circle"></i>
                                <strong>Chào mừng ${isAdmin ? 'Admin' : 'học viên'}!</strong> 
                                Bạn có quyền truy cập vào lớp Python - A.
                                ${isAdmin ? '<br><small>Bạn đang ở chế độ Admin - có thể truy cập tất cả các lớp học.</small>' : ''}
                            </div>
                        `;
                        accessMessage.style.display = 'block';
                        classContent.style.display = 'block';
                        lessonsSection.style.display = 'block';

                        // Load student count and display
                        loadStudentData();

                        // Check assignment status
                        checkAssignmentStatus(user, isAdmin);
                        checkAssignment2Status(user, isAdmin);
                        checkAssignment3Status(user, isAdmin);
                    } else {
                        // Deny access
                        const userClass = userData?.courseClass || 'chưa chọn lớp';
                        accessMessage.innerHTML = `
                            <div class="access-denied">
                                <i class="fas fa-exclamation-triangle"></i>
                                <strong>Không có quyền truy cập!</strong><br>
                                Bạn đã đăng ký lớp: <strong>${userClass}</strong><br>
                                Để truy cập lớp Python - A, vui lòng cập nhật thông tin lớp học trong trang 
                                <a href="../auth/">Tài Khoản</a>.
                            </div>
                        `;
                        accessMessage.style.display = 'block';
                    }
                } catch (error) {
                    console.error('Error checking user permissions:', error);
                    accessMessage.innerHTML = `
                        <div class="access-denied">
                            <i class="fas fa-exclamation-triangle"></i>
                            <strong>Lỗi kiểm tra quyền truy cập!</strong><br>
                            Vui lòng thử lại sau hoặc liên hệ admin.
                        </div>
                    `;
                    accessMessage.style.display = 'block';
                }
            } else {
                // User not logged in
                accessMessage.innerHTML = `
                    <div class="access-denied">
                        <i class="fas fa-sign-in-alt"></i>
                        <strong>Vui lòng đăng nhập!</strong><br>
                        Bạn cần đăng nhập để truy cập nội dung lớp học.
                        <br><a href="../auth/">Đăng nhập ngay</a>
                    </div>
                `;
                accessMessage.style.display = 'block';
            }
        });

        // Load student data for Python-A class
        async function loadStudentData() {
            try {
                const usersQuery = query(collection(db, "users"), where("courseClass", "==", "python-a"));
                const querySnapshot = await getDocs(usersQuery);
                const students = [];

                querySnapshot.forEach((doc) => {
                    const userData = doc.data();
                    if (userData.fullName) {
                        students.push({
                            name: userData.fullName,
                            avatar: userData.avatar || null
                        });
                    }
                });

                // Update student count
                document.getElementById('studentCount').textContent = `${students.length} học viên`;

                // Display student avatars (max 5)
                const avatarsContainer = document.getElementById('studentAvatars');
                avatarsContainer.innerHTML = '';

                // Display all students (up to 10)
                const displayStudents = students.slice(0, 10);
                displayStudents.forEach(student => {
                    const avatarDiv = document.createElement('div');
                    avatarDiv.className = 'student-avatar';
                    avatarDiv.setAttribute('data-name', student.name);

                    if (student.avatar) {
                        const img = document.createElement('img');
                        img.src = student.avatar;
                        img.alt = student.name;
                        avatarDiv.appendChild(img);
                    } else {
                        // Show first letter of name if no avatar
                        avatarDiv.textContent = student.name.charAt(0).toUpperCase();
                    }

                    avatarsContainer.appendChild(avatarDiv);
                });

                // Show "+" only if more than 10 students
                if (students.length > 10) {
                    const moreDiv = document.createElement('div');
                    moreDiv.className = 'student-avatar';
                    moreDiv.textContent = `+${students.length - 10}`;
                    moreDiv.setAttribute('data-name', `Và ${students.length - 10} học viên khác`);
                    avatarsContainer.appendChild(moreDiv);
                }

            } catch (error) {
                console.error('Error loading student data:', error);
                document.getElementById('studentCount').textContent = 'Không thể tải';
            }
        }

        // Check assignment completion status
        async function checkAssignmentStatus(user, isAdmin) {
            const statusElement = document.getElementById('assignmentStatus');
            const linkElement = document.getElementById('assignmentLink');
            const linkTextElement = document.getElementById('assignmentLinkText');

            if (!user) {
                statusElement.textContent = 'Cần đăng nhập';
                statusElement.style.color = '#6c757d';
                return;
            }

            try {
                // Check if assignment is completed
                const assignmentDoc = await getDoc(doc(db, "users", user.uid, "assignments", "assignment-1"));

                if (assignmentDoc.exists() && !isAdmin) {
                    // Assignment completed
                    const assignmentData = assignmentDoc.data();
                    const score = assignmentData.score || 0;
                    const totalQuestions = assignmentData.totalQuestions || 30;

                    statusElement.innerHTML = `Đã hoàn thành - Điểm: ${score}/${totalQuestions}`;
                    statusElement.style.color = '#28a745';

                    linkTextElement.textContent = 'Xem kết quả';
                    linkElement.style.background = 'linear-gradient(135deg, #28a745, #20c997)';
                } else {
                    // Assignment not completed or user is admin
                    if (isAdmin) {
                        statusElement.textContent = 'Chế độ Admin - Có thể làm thử';
                        statusElement.style.color = '#dc3545';
                        linkTextElement.textContent = 'Làm thử (Admin)';
                        linkElement.style.background = 'linear-gradient(135deg, #dc3545, #c82333)';
                    } else {
                        statusElement.textContent = 'Chưa hoàn thành';
                        statusElement.style.color = '#ffc107';
                        linkTextElement.textContent = 'Làm bài tập';
                    }
                }
            } catch (error) {
                console.error('Error checking assignment status:', error);
                statusElement.textContent = 'Lỗi kiểm tra trạng thái';
                statusElement.style.color = '#dc3545';
            }
        }

        // Check assignment 2 completion status (separated assignments)
        async function checkAssignment2Status(user, isAdmin) {
            const statusElement = document.getElementById('assignment2Status');
            const quizStatusElement = document.getElementById('quizAssignmentStatus');
            const codingStatusElement = document.getElementById('codingAssignmentStatus');
            const quizLinkElement = document.getElementById('quizAssignmentLink');
            const codingLinkElement = document.getElementById('codingAssignmentLink');
            const quizTextElement = document.getElementById('quizAssignmentText');
            const codingTextElement = document.getElementById('codingAssignmentText');

            if (!user) {
                statusElement.textContent = 'Cần đăng nhập';
                statusElement.style.color = '#6c757d';
                quizStatusElement.textContent = 'Cần đăng nhập';
                codingStatusElement.textContent = 'Cần đăng nhập';
                return;
            }

            try {
                // Check both quiz and coding assignments
                const quizDoc = await getDoc(doc(db, "users", user.uid, "assignments", "assignment-2"));
                const codingDoc = await getDoc(doc(db, "users", user.uid, "coding-assignments", "coding-assignment-2-a"));

                let quizCompleted = quizDoc.exists();
                let codingCompleted = false;
                let totalScore = 0;
                let quizScore = 0;
                let codingScore = 0;

                // Check quiz completion
                if (quizCompleted && !isAdmin) {
                    const quizData = quizDoc.data();
                    quizScore = quizData.score || 0; // Score is already calculated as /50
                    quizStatusElement.innerHTML = `<i class="fas fa-check-circle" style="color: #28a745;"></i> Hoàn thành (${quizScore}/50)`;
                    quizTextElement.textContent = 'Xem kết quả';
                    quizLinkElement.style.background = 'linear-gradient(135deg, #28a745, #20c997)';
                } else {
                    if (isAdmin) {
                        quizStatusElement.innerHTML = `<i class="fas fa-user-shield" style="color: #dc3545;"></i> Admin - Làm thử`;
                        quizTextElement.textContent = 'Làm thử (Admin)';
                        quizLinkElement.style.background = 'linear-gradient(135deg, #dc3545, #c82333)';
                    } else {
                        quizStatusElement.innerHTML = `<i class="fas fa-clock" style="color: #ffc107;"></i> Chưa hoàn thành`;
                        quizTextElement.textContent = 'Làm trắc nghiệm';
                    }
                }

                // Check coding completion
                if (codingDoc.exists()) {
                    const codingData = codingDoc.data();
                    const completedProblems = codingData.completedProblems || [];
                    const skippedProblems = codingData.skippedProblems || [];
                    codingCompleted = (completedProblems.length + skippedProblems.length) === 5;

                    if (codingCompleted && !isAdmin) {
                        let correctCount = 0;
                        const submissionResults = codingData.submissionResults || {};
                        for (let problemId of completedProblems) {
                            if (submissionResults[problemId] && submissionResults[problemId].isCorrect) {
                                correctCount++;
                            }
                        }
                        codingScore = Math.round((correctCount / 5) * 50);
                        codingStatusElement.innerHTML = `<i class="fas fa-check-circle" style="color: #28a745;"></i> Hoàn thành (${codingScore}/50)`;
                        codingTextElement.textContent = 'Xem kết quả';
                        codingLinkElement.style.background = 'linear-gradient(135deg, #28a745, #20c997)';
                    } else {
                        const progress = completedProblems.length + skippedProblems.length;
                        if (isAdmin) {
                            codingStatusElement.innerHTML = `<i class="fas fa-user-shield" style="color: #dc3545;"></i> Admin - Làm thử`;
                            codingTextElement.textContent = 'Làm thử (Admin)';
                            codingLinkElement.style.background = 'linear-gradient(135deg, #dc3545, #c82333)';
                        } else {
                            codingStatusElement.innerHTML = `<i class="fas fa-clock" style="color: #ffc107;"></i> Đang thực hiện (${progress}/5)`;
                            codingTextElement.textContent = 'Tiếp tục Code';
                        }
                    }
                } else {
                    if (isAdmin) {
                        codingStatusElement.innerHTML = `<i class="fas fa-user-shield" style="color: #dc3545;"></i> Admin - Làm thử`;
                        codingTextElement.textContent = 'Làm thử (Admin)';
                        codingLinkElement.style.background = 'linear-gradient(135deg, #dc3545, #c82333)';
                    } else {
                        codingStatusElement.innerHTML = `<i class="fas fa-clock" style="color: #ffc107;"></i> Chưa bắt đầu`;
                        codingTextElement.textContent = 'Thực hành Code';
                    }
                }

                // Update overall status
                totalScore = quizScore + codingScore;
                if (quizCompleted && codingCompleted && !isAdmin) {
                    statusElement.innerHTML = `Đã hoàn thành - Tổng điểm: ${totalScore}/100`;
                    statusElement.style.color = '#28a745';
                } else if ((quizCompleted || codingCompleted) && !isAdmin) {
                    const parts = [];
                    if (quizCompleted) parts.push('trắc nghiệm');
                    if (codingCompleted) parts.push('code');
                    statusElement.innerHTML = `Đã hoàn thành: ${parts.join(', ')} - Điểm hiện tại: ${totalScore}/100`;
                    statusElement.style.color = '#ffc107';
                } else {
                    if (isAdmin) {
                        statusElement.textContent = 'Chế độ Admin - Có thể làm thử cả 2 phần';
                        statusElement.style.color = '#dc3545';
                    } else {
                        statusElement.textContent = 'Chưa hoàn thành';
                        statusElement.style.color = '#ffc107';
                    }
                }
            } catch (error) {
                console.error('Error checking assignment 2 status:', error);
                console.error('Error code:', error.code);
                console.error('Error message:', error.message);

                if (error.code === 'permission-denied') {
                    statusElement.textContent = 'Lỗi quyền truy cập Firebase';
                    statusElement.style.color = '#dc3545';
                    quizStatusElement.textContent = 'Lỗi quyền truy cập';
                    codingStatusElement.textContent = 'Lỗi quyền truy cập';
                } else {
                    statusElement.textContent = 'Lỗi kiểm tra trạng thái';
                    statusElement.style.color = '#dc3545';
                    quizStatusElement.textContent = 'Lỗi kiểm tra';
                    codingStatusElement.textContent = 'Lỗi kiểm tra';
                }
            }
        }

        // Check assignment 3 completion status (quiz only)
        async function checkAssignment3Status(user, isAdmin) {
            const statusElement = document.getElementById('assignment3Status');
            const quizStatusElement = document.getElementById('lesson3QuizStatus');
            const codeStatusElement = document.getElementById('lesson3CodeStatus');
            const quizLinkElement = document.getElementById('lesson3QuizLink');
            const quizTextElement = document.getElementById('lesson3QuizText');

            if (!user) {
                statusElement.textContent = 'Cần đăng nhập';
                statusElement.style.color = '#6c757d';
                quizStatusElement.textContent = 'Cần đăng nhập';
                return;
            }

            try {
                // Check quiz assignment (lesson-3-quiz)
                const quizDoc = await getDoc(doc(db, "users", user.uid, "assignments", "lesson-3-quiz"));

                let quizCompleted = quizDoc.exists();
                let quizScore = 0;

                // Check quiz completion
                if (quizCompleted && !isAdmin) {
                    const quizData = quizDoc.data();
                    quizScore = quizData.score || 0; // Score out of 100
                    quizStatusElement.innerHTML = `<i class="fas fa-check-circle" style="color: #28a745;"></i> Hoàn thành (${quizScore}/100)`;
                    quizTextElement.textContent = 'Xem kết quả';
                    quizLinkElement.style.background = 'linear-gradient(135deg, #28a745, #20c997)';

                    statusElement.innerHTML = `Đã hoàn thành - Điểm: ${quizScore}/100`;
                    statusElement.style.color = '#28a745';
                } else {
                    if (isAdmin) {
                        quizStatusElement.innerHTML = `<i class="fas fa-user-shield" style="color: #dc3545;"></i> Admin - Làm thử`;
                        quizTextElement.textContent = 'Làm thử (Admin)';
                        quizLinkElement.style.background = 'linear-gradient(135deg, #dc3545, #c82333)';

                        statusElement.textContent = 'Chế độ Admin - Có thể làm thử';
                        statusElement.style.color = '#dc3545';
                    } else {
                        quizStatusElement.innerHTML = `<i class="fas fa-clock" style="color: #ffc107;"></i> Chưa hoàn thành`;
                        quizTextElement.textContent = 'Làm trắc nghiệm';

                        statusElement.textContent = 'Chưa hoàn thành';
                        statusElement.style.color = '#ffc107';
                    }
                }

                // Code exercises status (always practice mode)
                codeStatusElement.textContent = 'Bài tập luyện tập (không tính điểm)';

            } catch (error) {
                console.error('Error checking assignment 3 status:', error);
                statusElement.textContent = 'Lỗi kiểm tra trạng thái';
                statusElement.style.color = '#dc3545';
                quizStatusElement.textContent = 'Lỗi kiểm tra';
            }
        }

        // Show lesson function - now allows free access to all lessons
        async function showLesson(lessonNumber) {
            // No prerequisite check - students can access any lesson freely

            // Hide all lesson contents
            const allContents = document.querySelectorAll('.lesson-content');
            allContents.forEach(content => {
                content.classList.remove('active');
            });

            // Remove active class from all tabs
            const allTabs = document.querySelectorAll('.lesson-tab');
            allTabs.forEach(tab => {
                tab.classList.remove('active');
            });

            // Show selected lesson content
            const selectedContent = document.getElementById(`lesson${lessonNumber}`);
            if (selectedContent) {
                selectedContent.classList.add('active');
            }

            // Add active class to selected tab
            const selectedTab = document.querySelector(`.lesson-tab:nth-child(${lessonNumber})`);
            if (selectedTab) {
                selectedTab.classList.add('active');
            }
        }

        // Make showLesson function global
        window.showLesson = showLesson;
    </script>

    <script src="../assets/js/script.js"></script>
</body>
</html>
