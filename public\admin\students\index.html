<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON><PERSON> - VT Academy</title>
    <link rel="stylesheet" href="../../assets/css/style.css">
    <link rel="stylesheet" href="../css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .students-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
        }

        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid #ecf0f1;
        }

        .page-header h1 {
            color: #2c3e50;
            margin: 0;
            font-size: 2rem;
            font-weight: 700;
        }

        .back-btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 10px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .back-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .search-filter {
            display: flex;
            gap: 1rem;
            margin-bottom: 2rem;
            flex-wrap: wrap;
        }

        .search-box {
            flex: 1;
            min-width: 250px;
            position: relative;
        }

        .search-box input {
            width: 100%;
            padding: 0.75rem 1rem 0.75rem 3rem;
            border: 2px solid #ecf0f1;
            border-radius: 10px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }

        .search-box input:focus {
            outline: none;
            border-color: #3498db;
        }

        .search-box i {
            position: absolute;
            left: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: #7f8c8d;
        }

        .filter-select {
            padding: 0.75rem 1rem;
            border: 2px solid #ecf0f1;
            border-radius: 10px;
            font-size: 1rem;
            background: white;
            min-width: 150px;
        }

        .students-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
        }

        .students-table th,
        .students-table td {
            padding: 1rem;
            text-align: left;
            border-bottom: 1px solid #ecf0f1;
        }

        .students-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #2c3e50;
        }

        .student-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            object-fit: cover;
        }

        .student-info {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .class-badges {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        .class-badge {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .edit-btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: background 0.3s ease;
        }

        .edit-btn:hover {
            background: #2980b9;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
        }

        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 2rem;
            border-radius: 15px;
            width: 90%;
            max-width: 500px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid #ecf0f1;
        }

        .modal-header h2 {
            margin: 0;
            color: #2c3e50;
        }

        .close {
            color: #aaa;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }

        .close:hover {
            color: #000;
        }

        .class-checkboxes {
            display: grid;
            gap: 1rem;
            margin: 1rem 0;
        }

        .checkbox-item {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.75rem;
            border: 2px solid #ecf0f1;
            border-radius: 10px;
            transition: border-color 0.3s ease;
        }

        .checkbox-item:hover {
            border-color: #3498db;
        }

        .checkbox-item input[type="checkbox"] {
            width: 18px;
            height: 18px;
            accent-color: #3498db;
        }

        .checkbox-item label {
            font-weight: 500;
            color: #2c3e50;
            cursor: pointer;
            flex: 1;
        }

        .save-btn {
            background: linear-gradient(135deg, #43e97b, #38f9d7);
            color: white;
            border: none;
            padding: 0.75rem 2rem;
            border-radius: 10px;
            font-weight: 600;
            cursor: pointer;
            width: 100%;
            margin-top: 1rem;
            transition: transform 0.3s ease;
        }

        .save-btn:hover {
            transform: translateY(-2px);
        }

        .loading {
            text-align: center;
            padding: 2rem;
            color: #7f8c8d;
        }

        .no-students {
            text-align: center;
            padding: 3rem;
            color: #7f8c8d;
        }

        .no-students i {
            font-size: 3rem;
            margin-bottom: 1rem;
            color: #bdc3c7;
        }

        @media (max-width: 768px) {
            .search-filter {
                flex-direction: column;
            }
            
            .students-table {
                font-size: 0.9rem;
            }
            
            .students-table th,
            .students-table td {
                padding: 0.5rem;
            }
            
            .modal-content {
                margin: 10% auto;
                width: 95%;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header>
        <div class="container">
            <div class="logo">
                <img src="../../assets/images/logo.jpg" alt="VTA Logo">
            </div>
            <nav>
                <ul>
                    <li><a href="../../index.html">Trang Chủ</a></li>
                    <li><a href="../../classes/">Lớp Học</a></li>
                    <li><a href="../../achievements/">Thành Tích</a></li>
                    <li><a href="../../auth/register.html">Đăng Ký</a></li>
                    <li><a href="../../rankings/">Bảng Xếp Hạng</a></li>
                    <li><a href="../../research/">Nghiên Cứu</a></li>
                    <li><a href="../../auth/">Tài Khoản</a></li>
                    <li><a href="../" class="active">Quản Trị</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <!-- Main Content -->
    <main class="admin-main">
        <div class="container">
            <div class="students-container">
                <div class="page-header">
                    <h1><i class="fas fa-users-cog"></i> Quản Lý Học Viên</h1>
                    <a href="../" class="back-btn">
                        <i class="fas fa-arrow-left"></i> Quay lại
                    </a>
                </div>

                <!-- Search and Filter -->
                <div class="search-filter">
                    <div class="search-box">
                        <i class="fas fa-search"></i>
                        <input type="text" id="searchInput" placeholder="Tìm kiếm theo tên hoặc email...">
                    </div>
                    <select id="classFilter" class="filter-select">
                        <option value="">Tất cả lớp học</option>
                        <option value="python-a">Python & AI - Lớp A</option>
                        <option value="python-b">Python & AI - Lớp B</option>
                        <option value="python-c">Python & AI - Lớp C</option>
                        <option value="no-class">Chưa chọn lớp</option>
                    </select>
                </div>

                <!-- Students Table -->
                <div id="studentsTableContainer">
                    <div class="loading">
                        <i class="fas fa-spinner fa-spin"></i>
                        <p>Đang tải danh sách học viên...</p>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Edit Student Modal -->
    <div id="editModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Chỉnh sửa quyền truy cập lớp học</h2>
                <span class="close">&times;</span>
            </div>
            <div class="modal-body">
                <p><strong>Học viên:</strong> <span id="editStudentName"></span></p>
                <p><strong>Email:</strong> <span id="editStudentEmail"></span></p>
                
                <h3>Chọn lớp học được phép truy cập:</h3>
                <div class="class-checkboxes" id="classCheckboxes">
                    <!-- Checkboxes will be populated by JavaScript -->
                </div>
                
                <button id="saveChanges" class="save-btn">
                    <i class="fas fa-save"></i> Lưu thay đổi
                </button>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-section footer-logo">
                    <div class="footer-logo-container">
                        <img src="../../assets/images/logo.jpg" alt="VTA Logo">
                        <div class="logo-text">Vthon Academy</div>
                    </div>
                    <div class="slogan">Học, học nữa, học mãi.</div>
                    <div class="footer-bottom">
                        <p>&copy; 2025 – All rights reserved.</p>
                    </div>
                </div>

                <div class="footer-section footer-contact">
                    <h3>Liên hệ</h3>
                    <p><i class="fas fa-envelope"></i> <EMAIL></p>
                    <p><i class="fas fa-phone"></i> 0399787678</p>
                    <p><i class="fab fa-facebook-messenger"></i> Zalo: 0399787678</p>
                </div>

                <div class="footer-section footer-social">
                    <h3>Mạng xã hội</h3>
                    <div class="social-links">
                        <a href="https://www.facebook.com/vinhle030904/" target="_blank" class="social-link facebook">
                            <i class="fab fa-facebook-f"></i> Facebook
                        </a>
                        <a href="https://www.tiktok.com/@sunnii39" target="_blank" class="social-link tiktok">
                            <i class="fab fa-tiktok"></i> TikTok
                        </a>
                        <a href="https://www.instagram.com/vlee.39/?hl=en" target="_blank" class="social-link instagram">
                            <i class="fab fa-instagram"></i> Instagram
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Firebase SDK -->
    <script type="module">
        // Import Firebase modules
        import { initializeApp } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-app.js";
        import { getAuth, onAuthStateChanged } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-auth.js";
        import {
            getFirestore,
            doc,
            getDoc,
            updateDoc,
            collection,
            query,
            getDocs,
            where
        } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-firestore.js";

        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyBKNo8y_MOKYc3f3UdNRFwcMgeLRW71WXA",
            authDomain: "classroom-web-48bc2.firebaseapp.com",
            projectId: "classroom-web-48bc2",
            storageBucket: "classroom-web-48bc2.firebasestorage.app",
            messagingSenderId: "446746787502",
            appId: "1:446746787502:web:48d5ffd5a0b2c6e043b73f",
            measurementId: "G-742XRP9E96"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const auth = getAuth(app);
        const db = getFirestore(app);

        let allStudents = [];
        let currentEditingStudent = null;

        // Check admin access
        onAuthStateChanged(auth, async (user) => {
            if (!user || user.email !== '<EMAIL>') {
                window.location.href = '../../auth/';
                return;
            }

            await loadStudents();
            setupEventListeners();
        });

        // Load all students
        async function loadStudents() {
            try {
                const usersQuery = query(collection(db, "users"));
                const usersSnapshot = await getDocs(usersQuery);

                allStudents = [];
                usersSnapshot.forEach((doc) => {
                    const userData = doc.data();
                    if (userData.email !== '<EMAIL>' && userData.fullName) {
                        allStudents.push({
                            id: doc.id,
                            ...userData
                        });
                    }
                });

                displayStudents(allStudents);
            } catch (error) {
                console.error('Error loading students:', error);
                showError('Lỗi khi tải danh sách học viên');
            }
        }

        // Display students in table
        function displayStudents(students) {
            const container = document.getElementById('studentsTableContainer');

            if (students.length === 0) {
                container.innerHTML = `
                    <div class="no-students">
                        <i class="fas fa-users"></i>
                        <h3>Chưa có học viên nào</h3>
                        <p>Danh sách học viên sẽ hiển thị ở đây khi có người đăng ký</p>
                    </div>
                `;
                return;
            }

            let tableHTML = `
                <table class="students-table">
                    <thead>
                        <tr>
                            <th>Học viên</th>
                            <th>Email</th>
                            <th>Lớp học</th>
                            <th>Ngày tạo</th>
                            <th>Thao tác</th>
                        </tr>
                    </thead>
                    <tbody>
            `;

            students.forEach(student => {
                const classes = getStudentClasses(student);
                const classBadges = classes.map(cls =>
                    `<span class="class-badge">${getClassName(cls)}</span>`
                ).join('');

                const createdDate = student.createdAt ?
                    new Date(student.createdAt).toLocaleDateString('vi-VN') : 'N/A';

                tableHTML += `
                    <tr>
                        <td>
                            <div class="student-info">
                                <img src="${student.avatar ? student.avatar.replace('../assets/', '../../assets/') : '../../assets/images/avatars/avatar_boy_1.png'}"
                                     alt="Avatar" class="student-avatar">
                                <span>${student.fullName}</span>
                            </div>
                        </td>
                        <td>${student.email}</td>
                        <td>
                            <div class="class-badges">
                                ${classBadges || '<span style="color: #7f8c8d;">Chưa chọn lớp</span>'}
                            </div>
                        </td>
                        <td>${createdDate}</td>
                        <td>
                            <button class="edit-btn" onclick="editStudent('${student.id}')">
                                <i class="fas fa-edit"></i> Chỉnh sửa
                            </button>
                        </td>
                    </tr>
                `;
            });

            tableHTML += `
                    </tbody>
                </table>
            `;

            container.innerHTML = tableHTML;
        }

        // Get student classes
        function getStudentClasses(student) {
            const classes = [];

            // Check allowedClasses (new format)
            if (student.allowedClasses && Array.isArray(student.allowedClasses)) {
                classes.push(...student.allowedClasses);
            }

            // Check courseClass (old format) if allowedClasses doesn't exist
            if (classes.length === 0 && student.courseClass) {
                classes.push(student.courseClass);
            }

            return [...new Set(classes)]; // Remove duplicates
        }

        // Get class display name
        function getClassName(classId) {
            const classNames = {
                'python-a': 'Lớp A',
                'python-b': 'Lớp B',
                'python-c': 'Lớp C'
            };
            return classNames[classId] || classId;
        }

        // Edit student function (global scope)
        window.editStudent = async function(studentId) {
            const student = allStudents.find(s => s.id === studentId);
            if (!student) return;

            currentEditingStudent = student;

            // Update modal content
            document.getElementById('editStudentName').textContent = student.fullName;
            document.getElementById('editStudentEmail').textContent = student.email;

            // Setup class checkboxes
            const checkboxContainer = document.getElementById('classCheckboxes');
            const studentClasses = getStudentClasses(student);

            checkboxContainer.innerHTML = `
                <div class="checkbox-item">
                    <input type="checkbox" id="class-python-a" value="python-a"
                           ${studentClasses.includes('python-a') ? 'checked' : ''}>
                    <label for="class-python-a">Python & AI - Lớp A</label>
                </div>
                <div class="checkbox-item">
                    <input type="checkbox" id="class-python-b" value="python-b"
                           ${studentClasses.includes('python-b') ? 'checked' : ''}>
                    <label for="class-python-b">Python & AI - Lớp B</label>
                </div>
                <div class="checkbox-item">
                    <input type="checkbox" id="class-python-c" value="python-c"
                           ${studentClasses.includes('python-c') ? 'checked' : ''}>
                    <label for="class-python-c">Python & AI - Lớp C</label>
                </div>
            `;

            // Show modal
            document.getElementById('editModal').style.display = 'block';
        };

        // Setup event listeners
        function setupEventListeners() {
            // Search functionality
            document.getElementById('searchInput').addEventListener('input', filterStudents);
            document.getElementById('classFilter').addEventListener('change', filterStudents);

            // Modal close
            document.querySelector('.close').addEventListener('click', () => {
                document.getElementById('editModal').style.display = 'none';
            });

            // Save changes
            document.getElementById('saveChanges').addEventListener('click', saveStudentChanges);

            // Close modal when clicking outside
            window.addEventListener('click', (event) => {
                const modal = document.getElementById('editModal');
                if (event.target === modal) {
                    modal.style.display = 'none';
                }
            });
        }

        // Filter students
        function filterStudents() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            const classFilter = document.getElementById('classFilter').value;

            let filteredStudents = allStudents.filter(student => {
                const matchesSearch = student.fullName.toLowerCase().includes(searchTerm) ||
                                    student.email.toLowerCase().includes(searchTerm);

                if (!matchesSearch) return false;

                if (!classFilter) return true;

                if (classFilter === 'no-class') {
                    const classes = getStudentClasses(student);
                    return classes.length === 0;
                }

                const classes = getStudentClasses(student);
                return classes.includes(classFilter);
            });

            displayStudents(filteredStudents);
        }

        // Save student changes
        async function saveStudentChanges() {
            if (!currentEditingStudent) return;

            try {
                const checkboxes = document.querySelectorAll('#classCheckboxes input[type="checkbox"]');
                const selectedClasses = [];

                checkboxes.forEach(checkbox => {
                    if (checkbox.checked) {
                        selectedClasses.push(checkbox.value);
                    }
                });

                // Update student data
                const updateData = {
                    allowedClasses: selectedClasses,
                    lastUpdated: new Date().toISOString()
                };

                // Keep courseClass for backward compatibility (use first selected class)
                if (selectedClasses.length > 0) {
                    updateData.courseClass = selectedClasses[0];
                } else {
                    updateData.courseClass = null;
                }

                await updateDoc(doc(db, "users", currentEditingStudent.id), updateData);

                // Update local data
                const studentIndex = allStudents.findIndex(s => s.id === currentEditingStudent.id);
                if (studentIndex !== -1) {
                    allStudents[studentIndex] = {
                        ...allStudents[studentIndex],
                        ...updateData
                    };
                }

                // Refresh display
                filterStudents();

                // Close modal
                document.getElementById('editModal').style.display = 'none';

                showSuccess('Cập nhật quyền truy cập lớp học thành công!');

            } catch (error) {
                console.error('Error updating student:', error);
                showError('Lỗi khi cập nhật thông tin học viên');
            }
        }

        // Utility functions
        function showError(message) {
            const errorDiv = document.createElement('div');
            errorDiv.className = 'error-message';
            errorDiv.textContent = message;

            const container = document.querySelector('.container');
            container.insertBefore(errorDiv, container.firstChild);

            setTimeout(() => {
                errorDiv.remove();
            }, 5000);
        }

        function showSuccess(message) {
            const successDiv = document.createElement('div');
            successDiv.className = 'success-message';
            successDiv.textContent = message;

            const container = document.querySelector('.container');
            container.insertBefore(successDiv, container.firstChild);

            setTimeout(() => {
                successDiv.remove();
            }, 5000);
        }
    </script>
</body>
</html>
