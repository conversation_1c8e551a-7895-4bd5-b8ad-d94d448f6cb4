<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bài Tập Code 2: <PERSON> C<PERSON>n - Python A</title>
    <link rel="stylesheet" href="../../../assets/css/styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        .assignment-container {
            max-width: 1200px;
            margin: 120px auto 50px;
            padding: 0 20px;
        }
        
        .assignment-header {
            text-align: center;
            margin-bottom: 30px;
            padding: 25px;
            background: linear-gradient(135deg, #4285F4, #ff7aa8);
            color: white;
            border-radius: 15px;
        }
        
        .assignment-header h1 {
            font-size: 2rem;
            margin-bottom: 10px;
        }
        
        .coding-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .problems-panel {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
            padding: 20px;
            max-height: 80vh;
            overflow-y: auto;
        }
        
        .code-panel {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
            padding: 20px;
        }
        
        .problem-tab {
            display: inline-block;
            padding: 10px 20px;
            margin: 5px;
            background: #f8f9fa;
            border: 2px solid #e0e0e0;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s;
            font-weight: 500;
        }
        
        .problem-tab.active {
            background: #4285F4;
            color: white;
            border-color: #4285F4;
        }
        
        .problem-tab.completed {
            background: #28a745;
            color: white;
            border-color: #28a745;
        }

        .problem-tab.skipped {
            background: #6c757d;
            color: white;
            border-color: #6c757d;
        }
        
        .problem-content {
            display: none;
            margin-top: 20px;
        }
        
        .problem-content.active {
            display: block;
        }
        
        .problem-title {
            color: #4285F4;
            font-size: 1.3rem;
            margin-bottom: 15px;
        }
        
        .problem-description {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 15px;
            line-height: 1.6;
        }
        
        .expected-output {
            background: #e8f5e8;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 15px;
            border-left: 4px solid #28a745;
        }
        
        .expected-output h4 {
            color: #28a745;
            margin-bottom: 10px;
        }
        
        .code-output {
            background: #1e1e1e;
            color: #d4d4d4;
            padding: 10px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
        }
        
        .code-editor {
            width: 100%;
            height: 300px;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            resize: vertical;
            background: #f8f9fa;
        }
        
        .code-actions {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s;
        }
        
        .btn-run {
            background: #4285F4;
            color: white;
        }
        
        .btn-run:hover {
            background: #3367D6;
        }
        
        .btn-submit {
            background: #28a745;
            color: white;
        }
        
        .btn-submit:hover {
            background: #218838;
        }
        
        .btn-reset {
            background: #6c757d;
            color: white;
        }
        
        .btn-reset:hover {
            background: #5a6268;
        }

        .btn-skip {
            background: #ffc107;
            color: #212529;
        }

        .btn-skip:hover {
            background: #e0a800;
        }

        .info-panel {
            margin-top: 20px;
            padding: 15px;
            background: #e3f2fd;
            border-radius: 8px;
            border: 1px solid #bbdefb;
        }

        .info-title {
            font-weight: bold;
            margin-bottom: 10px;
            color: #1976d2;
        }

        .info-content {
            color: #333;
            line-height: 1.6;
        }
        
        .output-panel {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #ddd;
        }
        
        .output-title {
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }
        
        .output-content {
            background: #1e1e1e;
            color: #d4d4d4;
            padding: 10px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            min-height: 100px;
        }
        
        .result-panel {
            margin-top: 15px;
            padding: 15px;
            border-radius: 8px;
            display: none;
        }
        
        .result-success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        
        .result-error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        
        .progress-panel {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
            padding: 20px;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e0e0e0;
            border-radius: 10px;
            overflow: hidden;
            margin: 15px 0;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4285F4, #28a745);
            transition: width 0.3s ease;
            border-radius: 10px;
        }
        
        .back-link {
            display: inline-block;
            color: #4285F4;
            text-decoration: none;
            margin-bottom: 20px;
            font-weight: 500;
        }
        
        .back-link:hover {
            text-decoration: underline;
        }
        
        @media (max-width: 768px) {
            .coding-container {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation Bar -->
    <header>
        <div class="container">
            <div class="logo">
                <img src="../../../assets/images/logo.jpg" alt="VTA Logo">
            </div>
            <nav>
                <ul>
                    <li><a href="../../../index.html">Trang Chủ</a></li>
                    <li><a href="../../index.html">Lớp Học</a></li>
                    <li><a href="../../../achievements/">Thành Tích</a></li>
                    <li><a href="../../../auth/register.html">Đăng Ký</a></li>
                    <li><a href="../../../rankings/">Bảng Xếp Hạng</a></li>
                    <li><a href="../../../research/">Nghiên Cứu</a></li>
                    <li><a href="../../../auth/">Tài Khoản</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <div class="assignment-container">
        <a href="../../python-a.html" class="back-link">
            <i class="fas fa-arrow-left"></i> Quay lại lớp Python - A
        </a>

        <div class="assignment-header">
            <h1>Bài Tập Code 2: Python Cơ Bản</h1>
            <p>5 bài tập lập trình - Thực hành với print() và comment</p>
        </div>

        <!-- Progress Panel -->
        <div class="progress-panel">
            <h3>Tiến độ hoàn thành</h3>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill" style="width: 0%"></div>
            </div>
            <p><span id="completedCount">0 nộp, 0 bỏ qua</span> / 5 bài</p>
        </div>

        <!-- Coding Container -->
        <div class="coding-container">
            <!-- Problems Panel -->
            <div class="problems-panel">
                <h3>Danh sách bài tập</h3>
                
                <!-- Problem Tabs -->
                <div class="problem-tabs">
                    <div class="problem-tab active" onclick="showProblem(1)" id="tab1">Bài 1</div>
                    <div class="problem-tab" onclick="showProblem(2)" id="tab2">Bài 2</div>
                    <div class="problem-tab" onclick="showProblem(3)" id="tab3">Bài 3</div>
                    <div class="problem-tab" onclick="showProblem(4)" id="tab4">Bài 4</div>
                    <div class="problem-tab" onclick="showProblem(5)" id="tab5">Bài 5</div>
                </div>

                <!-- Problem 1 -->
                <div class="problem-content active" id="problem1">
                    <div class="problem-title">Bài 1: Lời Chào Tiêu Chuẩn</div>
                    <div class="problem-description">
                        <strong>Tên file:</strong> loi_chao.py<br>
                        <strong>Yêu cầu:</strong> Viết một chương trình Python để in ra màn hình chính xác dòng chữ sau:
                    </div>
                    <div class="expected-output">
                        <h4><i class="fas fa-terminal"></i> Output mong đợi:</h4>
                        <div class="code-output">Chao mung den voi khoa hoc Python va AI!</div>
                    </div>
                </div>

                <!-- Problem 2 -->
                <div class="problem-content" id="problem2">
                    <div class="problem-title">Bài 2: Thông Tin Học Viên</div>
                    <div class="problem-description">
                        <strong>Tên file:</strong> thong_tin_hoc_vien.py<br>
                        <strong>Yêu cầu:</strong> Viết một chương trình Python để in ra màn hình chính xác 3 dòng thông tin sau đây.
                    </div>
                    <div class="expected-output">
                        <h4><i class="fas fa-terminal"></i> Output mong đợi:</h4>
                        <div class="code-output">Ho va ten: Nguyen Van An
Lop: Python Co Ban
Muc tieu: Tro thanh lap trinh vien Python.</div>
                    </div>
                </div>

                <!-- Problem 3 -->
                <div class="problem-content" id="problem3">
                    <div class="problem-title">Bài 3: Ghi Chú Đúng Cách</div>
                    <div class="problem-description">
                        <strong>Tên file:</strong> ghi_chu_code.py<br>
                        <strong>Yêu cầu:</strong> Viết một chương trình Python thực hiện các yêu cầu sau:<br>
                        • Dòng đầu tiên của file phải là một comment ghi tên của bạn, ví dụ: # Nguyen Van B<br>
                        • Dòng thứ hai in ra màn hình chính xác dòng chữ: Lap trinh Python that thu vi!<br>
                        • Dòng thứ ba là một comment giải thích mục đích của dòng lệnh print ở trên.
                    </div>
                    <div class="expected-output">
                        <h4><i class="fas fa-terminal"></i> Output mong đợi:</h4>
                        <div class="code-output">Lap trinh Python that thu vi!</div>
                    </div>
                </div>

                <!-- Problem 4 -->
                <div class="problem-content" id="problem4">
                    <div class="problem-title">Bài 4: In Chuỗi Đặc Biệt</div>
                    <div class="problem-description">
                        <strong>Tên file:</strong> chuoi_dac_biet.py<br>
                        <strong>Yêu cầu:</strong> Viết một chương trình Python để in ra màn hình chính xác chuỗi ký tự sau, bao gồm cả các dấu ngoặc kép và dấu chấm than.
                    </div>
                    <div class="expected-output">
                        <h4><i class="fas fa-terminal"></i> Output mong đợi:</h4>
                        <div class="code-output">Python noi: "Hello, World!" Lap trinh rat vui!!!</div>
                    </div>
                </div>

                <!-- Problem 5 -->
                <div class="problem-content" id="problem5">
                    <div class="problem-title">Bài 5: Bài Thơ Con Cóc</div>
                    <div class="problem-description">
                        <strong>Tên file:</strong> bai_tho.py<br>
                        <strong>Yêu cầu:</strong> Viết một chương trình Python để in ra màn hình chính xác 4 dòng của một "bài thơ con cóc" sau đây. Mỗi dòng thơ phải được in trên một dòng riêng biệt.
                    </div>
                    <div class="expected-output">
                        <h4><i class="fas fa-terminal"></i> Output mong đợi:</h4>
                        <div class="code-output">Dong code dau tien,
Python chay rat em.
Man hinh hien loi chao,
Long vui sao la vui!</div>
                    </div>
                </div>
            </div>

            <!-- Code Panel -->
            <div class="code-panel">
                <h3>Trình soạn thảo code</h3>
                <textarea class="code-editor" id="codeEditor" placeholder="# Viết code Python của bạn ở đây..."></textarea>
                
                <div class="code-actions">
                    <button class="btn btn-submit" onclick="submitCode()">
                        <i class="fas fa-check"></i> Nộp Bài
                    </button>
                    <button class="btn btn-skip" onclick="skipProblem()">
                        <i class="fas fa-forward"></i> Bỏ Qua
                    </button>
                    <button class="btn btn-reset" onclick="resetCode()">
                        <i class="fas fa-undo"></i> Reset
                    </button>
                </div>

                <!-- Info Panel -->
                <div class="info-panel">
                    <div class="info-title"><i class="fas fa-info-circle"></i> Lưu ý:</div>
                    <div class="info-content">
                        • Bạn chỉ có thể nộp bài một lần cho mỗi câu hỏi<br>
                        • Không thể xem kết quả ngay lập tức<br>
                        • Nếu không biết làm, hãy bấm "Bỏ Qua"<br>
                        • Điểm số sẽ được tính dựa trên độ chính xác cuối cùng
                    </div>
                </div>

                <!-- Result Panel -->
                <div class="result-panel" id="resultPanel">
                    <div id="resultContent"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-section footer-logo">
                    <div class="footer-logo-container">
                        <img src="../../../assets/images/logo.jpg" alt="VTA Logo">
                        <div class="logo-text">Vthon Academy</div>
                    </div>
                    <div class="slogan">Học, học nữa, học mãi.</div>
                    <div class="footer-bottom">
                        <p>&copy; 2025 – All rights reserved.</p>
                    </div>
                </div>

                <div class="footer-section footer-contact">
                    <h3>Liên hệ</h3>
                    <p><i class="fas fa-envelope"></i> <EMAIL></p>
                    <p><i class="fas fa-phone"></i> 0399787678</p>
                    <p><i class="fab fa-facebook-messenger"></i> Zalo: 0399787678</p>
                </div>

                <div class="footer-section footer-social">
                    <h3>Mạng xã hội</h3>
                    <div class="social-links">
                        <a href="https://www.facebook.com/vinhle030904/" target="_blank" class="social-link facebook">
                            <i class="fab fa-facebook-f"></i> Facebook
                        </a>
                        <a href="https://www.tiktok.com/@sunnii39" target="_blank" class="social-link tiktok">
                            <i class="fab fa-tiktok"></i> TikTok
                        </a>
                        <a href="https://www.instagram.com/vlee.39/?hl=en" target="_blank" class="social-link instagram">
                            <i class="fab fa-instagram"></i> Instagram
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Firebase SDK -->
    <script type="module">
        // Import Firebase modules
        import { initializeApp } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-app.js";
        import { getAuth, onAuthStateChanged } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-auth.js";
        import { getFirestore, doc, getDoc, updateDoc, setDoc } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-firestore.js";

        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyBKNo8y_MOKYc3f3UdNRFwcMgeLRW71WXA",
            authDomain: "classroom-web-48bc2.firebaseapp.com",
            projectId: "classroom-web-48bc2",
            storageBucket: "classroom-web-48bc2.firebasestorage.app",
            messagingSenderId: "446746787502",
            appId: "1:446746787502:web:48d5ffd5a0b2c6e043b73f",
            measurementId: "G-742XRP9E96"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const auth = getAuth(app);
        const db = getFirestore(app);

        // Global variables
        let currentProblem = 1;
        let completedProblems = new Set();
        let skippedProblems = new Set();
        let problemSolutions = {};
        let submissionResults = {};

        // Expected outputs for each problem
        const expectedOutputs = {
            1: "Chao mung den voi khoa hoc Python va AI!",
            2: "Ho va ten: Nguyen Van An\nLop: Python Co Ban\nMuc tieu: Tro thanh lap trinh vien Python.",
            3: "Lap trinh Python that thu vi!",
            4: 'Python noi: "Hello, World!" Lap trinh rat vui!!!',
            5: "Dong code dau tien,\nPython chay rat em.\nMan hinh hien loi chao,\nLong vui sao la vui!"
        };

        // Initialize when page loads
        window.addEventListener('DOMContentLoaded', function() {
            checkAuthAndLoadProgress();
        });

        // Page unload warning removed - no longer needed

        // Check if user is admin
        function isAdmin(user) {
            return user && user.email === '<EMAIL>';
        }

        // Check authentication and load progress
        async function checkAuthAndLoadProgress() {
            onAuthStateChanged(auth, async (user) => {
                console.log('Auth state changed:', user ? 'User logged in' : 'No user');
                console.log('User details:', user ? { uid: user.uid, email: user.email } : 'null');

                if (!user) {
                    alert('Bạn cần đăng nhập để làm bài tập!');
                    window.location.href = '../../../auth/';
                    return;
                }

                try {
                    console.log('Attempting to load progress for user:', user.uid);
                    // Load saved progress
                    const progressDoc = await getDoc(doc(db, "users", user.uid, "coding-assignments", "coding-assignment-2-a"));
                    console.log('Progress doc exists:', progressDoc.exists());
                    if (progressDoc.exists()) {
                        const progressData = progressDoc.data();
                        completedProblems = new Set(progressData.completedProblems || []);
                        skippedProblems = new Set(progressData.skippedProblems || []);
                        problemSolutions = progressData.problemSolutions || {};
                        submissionResults = progressData.submissionResults || {};

                        // Update UI
                        updateProgress();
                        updateProblemTabs();

                        // Check if assignment is completed and user is not admin
                        const totalAttempted = completedProblems.size + skippedProblems.size;
                        console.log('Total attempted:', totalAttempted, 'Is admin:', isAdmin(user));

                        if (totalAttempted === 5 && !isAdmin(user)) {
                            // Show completion results for regular users
                            console.log('Showing completion results for regular user');
                            try {
                                // Add small delay to ensure DOM is ready
                                setTimeout(() => {
                                    showCompletionResults();
                                }, 100);
                                return; // Exit early to prevent further execution
                            } catch (error) {
                                console.error('Error showing completion results:', error);
                                alert('Có lỗi khi hiển thị kết quả. Vui lòng tải lại trang.');
                            }
                        } else {
                            // Load saved code for current problem
                            if (problemSolutions[currentProblem]) {
                                document.getElementById('codeEditor').value = problemSolutions[currentProblem];
                            }

                            // Show admin notification if admin and assignment completed
                            if (totalAttempted === 5 && isAdmin(user)) {
                                console.log('Showing admin notification');
                                showAdminNotification();
                            }
                        }
                    }
                } catch (error) {
                    console.error('Error loading progress:', error);
                    console.error('Error code:', error.code);
                    console.error('Error message:', error.message);

                    if (error.code === 'permission-denied') {
                        alert('⚠️ Lỗi quyền truy cập Firebase. Vui lòng kiểm tra cấu hình Security Rules.');
                    }
                }
            });
        }

        // Show problem function
        function showProblem(problemNumber) {
            // Save current code before switching
            if (document.getElementById('codeEditor').value.trim()) {
                problemSolutions[currentProblem] = document.getElementById('codeEditor').value;
            }

            currentProblem = problemNumber;

            // Hide all problem contents
            document.querySelectorAll('.problem-content').forEach(content => {
                content.classList.remove('active');
            });

            // Remove active class from all tabs
            document.querySelectorAll('.problem-tab').forEach(tab => {
                tab.classList.remove('active');
            });

            // Show selected problem
            document.getElementById(`problem${problemNumber}`).classList.add('active');
            document.getElementById(`tab${problemNumber}`).classList.add('active');

            // Load saved code for this problem
            const savedCode = problemSolutions[problemNumber] || '';
            document.getElementById('codeEditor').value = savedCode;

            // Check if problem is completed or skipped
            const isCompleted = completedProblems.has(problemNumber);
            const isSkipped = skippedProblems.has(problemNumber);

            // Disable editor if already submitted or skipped
            document.getElementById('codeEditor').disabled = isCompleted || isSkipped;

            // Update button states
            const submitBtn = document.querySelector('.btn-submit');
            const skipBtn = document.querySelector('.btn-skip');

            if (isCompleted) {
                submitBtn.textContent = 'Đã Nộp';
                submitBtn.disabled = true;
                skipBtn.style.display = 'none';
            } else if (isSkipped) {
                submitBtn.style.display = 'none';
                skipBtn.textContent = 'Đã Bỏ Qua';
                skipBtn.disabled = true;
            } else {
                submitBtn.textContent = 'Nộp Bài';
                submitBtn.disabled = false;
                skipBtn.textContent = 'Bỏ Qua';
                skipBtn.disabled = false;
                submitBtn.style.display = 'inline-block';
                skipBtn.style.display = 'inline-block';
            }

            // Clear result panel
            document.getElementById('resultPanel').style.display = 'none';
        }

        // Skip problem function
        function skipProblem() {
            if (confirm('Bạn có chắc muốn bỏ qua bài này? Bạn sẽ không được điểm cho bài này.')) {
                skippedProblems.add(currentProblem);

                // Update UI
                updateProgress();
                updateProblemTabs();

                // Disable editor and buttons
                document.getElementById('codeEditor').disabled = true;
                document.querySelector('.btn-submit').style.display = 'none';
                document.querySelector('.btn-skip').textContent = 'Đã Bỏ Qua';
                document.querySelector('.btn-skip').disabled = true;

                // Save progress
                saveProgress();

                // Show result
                const resultPanel = document.getElementById('resultPanel');
                const resultContent = document.getElementById('resultContent');
                resultPanel.className = 'result-panel result-error';
                resultContent.innerHTML = `
                    <i class="fas fa-forward"></i>
                    <strong>Đã bỏ qua bài ${currentProblem}</strong><br>
                    Bạn có thể tiếp tục với bài tiếp theo.
                `;
                resultPanel.style.display = 'block';
            }
        }

        // Simulate Python code execution
        function simulatePythonExecution(code) {
            // Remove comments and empty lines for execution
            const lines = code.split('\n');
            let output = '';

            for (let line of lines) {
                line = line.trim();
                if (line.startsWith('#') || line === '') continue;

                // Handle print statements
                const printMatch = line.match(/print\s*\(\s*["']([^"']*)["']\s*\)/);
                if (printMatch) {
                    output += printMatch[1] + '\n';
                }
            }

            return output.trim();
        }

        // Submit code function
        async function submitCode() {
            const code = document.getElementById('codeEditor').value;
            const resultPanel = document.getElementById('resultPanel');
            const resultContent = document.getElementById('resultContent');

            if (!code.trim()) {
                alert('Vui lòng nhập code trước khi nộp!');
                return;
            }

            if (!confirm('Bạn có chắc muốn nộp bài? Bạn chỉ có thể nộp một lần cho mỗi bài.')) {
                return;
            }

            try {
                // Evaluate the code and store result (but don't show to user)
                const actualOutput = simulatePythonExecution(code);
                const expectedOutput = expectedOutputs[currentProblem];
                const isCorrect = actualOutput === expectedOutput;

                // Mark as completed and save solution
                completedProblems.add(currentProblem);
                problemSolutions[currentProblem] = code;
                submissionResults[currentProblem] = {
                    code: code,
                    actualOutput: actualOutput,
                    expectedOutput: expectedOutput,
                    isCorrect: isCorrect,
                    submittedAt: new Date().toISOString()
                };

                // Update UI
                updateProgress();
                updateProblemTabs();

                // Disable editor and buttons
                document.getElementById('codeEditor').disabled = true;
                document.querySelector('.btn-submit').textContent = 'Đã Nộp';
                document.querySelector('.btn-submit').disabled = true;
                document.querySelector('.btn-skip').style.display = 'none';

                // Save progress to Firebase
                await saveProgress();

                // Show submission confirmation (without revealing correctness)
                resultPanel.className = 'result-panel result-success';
                resultContent.innerHTML = `
                    <i class="fas fa-check-circle"></i>
                    <strong>Đã nộp bài ${currentProblem} thành công!</strong><br>
                    Kết quả sẽ được công bố sau khi hoàn thành tất cả bài tập.
                `;
                resultPanel.style.display = 'block';

            } catch (error) {
                resultPanel.className = 'result-panel result-error';
                resultContent.innerHTML = `
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>Lỗi:</strong> ${error.message}
                `;
                resultPanel.style.display = 'block';
            }
        }

        // Reset code function
        function resetCode() {
            if (confirm('Bạn có chắc muốn xóa toàn bộ code hiện tại?')) {
                document.getElementById('codeEditor').value = '';
                document.getElementById('outputContent').textContent = 'Nhấn "Chạy Code" để xem kết quả...';
                document.getElementById('resultPanel').style.display = 'none';
            }
        }

        // Update progress
        function updateProgress() {
            const completedCount = completedProblems.size;
            const skippedCount = skippedProblems.size;
            const totalAttempted = completedCount + skippedCount;
            const progressPercent = (totalAttempted / 5) * 100;

            document.getElementById('progressFill').style.width = progressPercent + '%';
            document.getElementById('completedCount').textContent = `${completedCount} nộp, ${skippedCount} bỏ qua`;
        }

        // Update problem tabs
        function updateProblemTabs() {
            for (let i = 1; i <= 5; i++) {
                const tab = document.getElementById(`tab${i}`);
                tab.classList.remove('completed', 'skipped');

                if (completedProblems.has(i)) {
                    tab.classList.add('completed');
                } else if (skippedProblems.has(i)) {
                    tab.classList.add('skipped');
                }
            }
        }

        // Save progress to Firebase
        async function saveProgress() {
            const user = auth.currentUser;
            if (!user) return;

            // Admin can do assignments but results won't be saved to score
            if (isAdmin(user)) {
                console.log('🔧 Admin mode: Coding assignment progress saved but score not added');

                const progressData = {
                    completedProblems: Array.from(completedProblems),
                    skippedProblems: Array.from(skippedProblems),
                    problemSolutions: problemSolutions,
                    submissionResults: submissionResults,
                    lastUpdated: new Date().toISOString(),
                    totalProblems: 5,
                    assignmentId: 'coding-assignment-2-a'
                };

                await setDoc(doc(db, "users", user.uid, "coding-assignments", "coding-assignment-2-a"), progressData);
                return; // Exit early for admin
            }

            try {
                const progressData = {
                    completedProblems: Array.from(completedProblems),
                    skippedProblems: Array.from(skippedProblems),
                    problemSolutions: problemSolutions,
                    submissionResults: submissionResults,
                    lastUpdated: new Date().toISOString(),
                    totalProblems: 5,
                    assignmentId: 'coding-assignment-2-a'
                };

                await setDoc(doc(db, "users", user.uid, "coding-assignments", "coding-assignment-2-a"), progressData);

                // Calculate score when all problems are attempted (completed + skipped = 5)
                const totalAttempted = completedProblems.size + skippedProblems.size;
                if (totalAttempted === 5) {
                    // Calculate accuracy and score
                    let correctCount = 0;
                    for (let problemId of completedProblems) {
                        if (submissionResults[problemId] && submissionResults[problemId].isCorrect) {
                            correctCount++;
                        }
                    }

                    const accuracy = correctCount / 5; // Out of 5 total problems
                    const codingScore = Math.round(accuracy * 50); // 50 points max for coding part

                    const userDoc = await getDoc(doc(db, "users", user.uid));
                    if (userDoc.exists()) {
                        const userData = userDoc.data();
                        const currentTotalScore = userData.totalScore || 0;
                        const newTotalScore = currentTotalScore + codingScore;

                        await updateDoc(doc(db, "users", user.uid), {
                            codingAssignmentCount: (userData.codingAssignmentCount || 0) + 1,
                            totalScore: newTotalScore,
                            lastCodingAssignmentDate: new Date().toISOString()
                        });

                        alert(`🎉 Hoàn thành bài tập code! Bạn làm đúng ${correctCount}/5 bài. Điểm code: ${codingScore}/50 điểm.`);
                    }
                }

            } catch (error) {
                console.error('Error saving progress:', error);
                console.error('Error code:', error.code);
                console.error('Error message:', error.message);

                if (error.code === 'permission-denied') {
                    alert('⚠️ Lỗi quyền truy cập Firebase. Không thể lưu tiến độ. Vui lòng liên hệ admin.');
                } else {
                    alert('⚠️ Có lỗi khi lưu tiến độ: ' + error.message);
                }
            }
        }

        // Show admin notification
        function showAdminNotification() {
            const notification = document.createElement('div');
            notification.style.cssText = `
                background: linear-gradient(135deg, #e74c3c, #c0392b);
                color: white;
                padding: 15px;
                margin-bottom: 20px;
                border-radius: 8px;
                text-align: center;
                font-weight: bold;
                border: 2px solid #a93226;
                position: fixed;
                top: 20px;
                left: 50%;
                transform: translateX(-50%);
                z-index: 1000;
                max-width: 500px;
            `;
            notification.innerHTML = `
                <i class="fas fa-user-shield"></i>
                <strong>Chế độ Admin:</strong> Bạn có thể xem và làm lại bài tập nhưng điểm số không được cập nhật.
                <button onclick="this.parentElement.remove()" style="background: none; border: none; color: white; float: right; cursor: pointer;">✕</button>
            `;
            document.body.appendChild(notification);

            // Auto remove after 5 seconds
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
            }, 5000);
        }

        // Show completion results
        function showCompletionResults() {
            console.log('showCompletionResults called');
            console.log('completedProblems:', completedProblems);
            console.log('submissionResults:', submissionResults);

            // Calculate final score
            let correctCount = 0;
            for (let problemId of completedProblems) {
                if (submissionResults[problemId] && submissionResults[problemId].isCorrect) {
                    correctCount++;
                }
            }
            const codingScore = Math.round((correctCount / 5) * 50);
            console.log('Calculated score:', codingScore, 'Correct count:', correctCount);

            // Find the assignment container
            const container = document.querySelector('.assignment-container');
            if (!container) {
                console.error('Assignment container not found!');
                return;
            }

            // Hide the assignment interface and show results
            container.innerHTML = `
                <a href="../../python-a.html" class="back-link">
                    <i class="fas fa-arrow-left"></i> Quay lại lớp Python - A
                </a>

                <div class="assignment-header">
                    <h1>Kết Quả Bài Tập Code 2</h1>
                    <p>Bài tập đã hoàn thành vào ${new Date().toLocaleString('vi-VN')}</p>
                </div>

                <div style="background: #e8f5e8; border: 1px solid #c3e6cb; color: #155724; padding: 20px; border-radius: 10px; margin-bottom: 30px; text-align: center;">
                    <i class="fas fa-check-circle" style="font-size: 2rem; margin-bottom: 10px;"></i>
                    <h3>🎉 Chúc mừng! Bạn đã hoàn thành bài tập code!</h3>
                    <p><strong>Kết quả:</strong> ${correctCount}/5 bài đúng</p>
                    <p><strong>Điểm số:</strong> ${codingScore}/50 điểm</p>
                </div>

                <div style="background: white; border-radius: 15px; box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1); padding: 30px;">
                    <h3><i class="fas fa-list-check"></i> Chi tiết kết quả từng bài:</h3>
                    <div id="detailedResults"></div>
                </div>
            `;

            // Show detailed results for each problem
            let detailedHTML = '';
            for (let i = 1; i <= 5; i++) {
                const isCompleted = completedProblems.has(i);
                const isSkipped = skippedProblems.has(i);
                const result = submissionResults[i];

                let statusIcon, statusText, statusClass;
                if (isCompleted && result) {
                    statusIcon = result.isCorrect ? '<i class="fas fa-check-circle" style="color: #28a745;"></i>' : '<i class="fas fa-times-circle" style="color: #dc3545;"></i>';
                    statusText = result.isCorrect ? 'Đúng' : 'Sai';
                    statusClass = result.isCorrect ? 'correct' : 'incorrect';
                } else if (isSkipped) {
                    statusIcon = '<i class="fas fa-forward" style="color: #ffc107;"></i>';
                    statusText = 'Đã bỏ qua';
                    statusClass = 'skipped';
                } else {
                    statusIcon = '<i class="fas fa-question-circle" style="color: #6c757d;"></i>';
                    statusText = 'Chưa làm';
                    statusClass = 'not-done';
                }

                detailedHTML += `
                    <div style="background: #f8f9fa; border-radius: 15px; padding: 25px; margin-bottom: 20px; border-left: 5px solid ${result && result.isCorrect ? '#28a745' : isSkipped ? '#ffc107' : '#6c757d'}; box-shadow: 0 3px 10px rgba(0,0,0,0.1);">
                        <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 20px;">
                            <h4 style="margin: 0; color: #333; font-size: 18px; font-weight: bold;">
                                Bài ${i}: ${getProblemTitle(i)}
                            </h4>
                            <div style="display: flex; align-items: center;">
                                ${result && result.isCorrect ?
                                    '<span style="background: #28a745; color: white; padding: 8px 16px; border-radius: 25px; font-size: 14px; font-weight: bold; display: flex; align-items: center; gap: 5px;"><i class="fas fa-check-circle"></i> Đúng</span>' :
                                    result ? '<span style="background: #dc3545; color: white; padding: 8px 16px; border-radius: 25px; font-size: 14px; font-weight: bold; display: flex; align-items: center; gap: 5px;"><i class="fas fa-times-circle"></i> Sai</span>' :
                                    isSkipped ? '<span style="background: #ffc107; color: white; padding: 8px 16px; border-radius: 25px; font-size: 14px; font-weight: bold; display: flex; align-items: center; gap: 5px;"><i class="fas fa-forward"></i> Bỏ qua</span>' :
                                    '<span style="background: #6c757d; color: white; padding: 8px 16px; border-radius: 25px; font-size: 14px; font-weight: bold; display: flex; align-items: center; gap: 5px;"><i class="fas fa-question-circle"></i> Chưa làm</span>'
                                }
                            </div>
                        </div>

                        ${result ? `
                            <!-- Đề bài -->
                            <div style="background: white; padding: 20px; border-radius: 12px; margin-bottom: 20px; border: 1px solid #e0e0e0; box-shadow: 0 2px 8px rgba(0,0,0,0.05);">
                                <h5 style="margin: 0 0 15px 0; color: #495057; display: flex; align-items: center; font-size: 16px; font-weight: 600;">
                                    <i class="fas fa-file-alt" style="margin-right: 8px; color: #6f42c1;"></i>
                                    Đề bài
                                </h5>
                                <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; border: 1px solid #e9ecef; color: #495057; font-size: 14px; line-height: 1.6;">
                                    ${getProblemDescription(i)}
                                </div>
                            </div>

                            <!-- Output cần có -->
                            <div style="background: white; padding: 20px; border-radius: 12px; margin-bottom: 20px; border: 1px solid #e0e0e0; box-shadow: 0 2px 8px rgba(0,0,0,0.05);">
                                <h5 style="margin: 0 0 15px 0; color: #495057; display: flex; align-items: center; font-size: 16px; font-weight: 600;">
                                    <i class="fas fa-bullseye" style="margin-right: 8px; color: #28a745;"></i>
                                    Output cần có
                                </h5>
                                <pre style="background: #e8f5e8; padding: 15px; border-radius: 8px; overflow-x: auto; font-family: 'Courier New', monospace; font-size: 13px; margin: 0; border: 1px solid #c3e6cb; line-height: 1.5; color: #155724; white-space: pre-wrap;">${getExpectedOutput(i)}</pre>
                            </div>

                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                                <!-- Code của học viên -->
                                <div style="background: white; padding: 20px; border-radius: 12px; border: 1px solid #e0e0e0; box-shadow: 0 2px 8px rgba(0,0,0,0.05);">
                                    <h5 style="margin: 0 0 15px 0; color: #495057; display: flex; align-items: center; font-size: 16px; font-weight: 600;">
                                        <i class="fas fa-code" style="margin-right: 8px; color: #007bff;"></i>
                                        Code bạn đã nộp
                                    </h5>
                                    <pre style="background: #f8f9fa; padding: 15px; border-radius: 8px; overflow-x: auto; font-family: 'Courier New', monospace; font-size: 13px; margin: 0; border: 1px solid #e9ecef; line-height: 1.5; color: #495057;">${result.code}</pre>
                                </div>

                                <!-- Code chính xác -->
                                <div style="background: white; padding: 20px; border-radius: 12px; border: 1px solid #e0e0e0; box-shadow: 0 2px 8px rgba(0,0,0,0.05);">
                                    <h5 style="margin: 0 0 15px 0; color: #495057; display: flex; align-items: center; font-size: 16px; font-weight: 600;">
                                        <i class="fas fa-lightbulb" style="margin-right: 8px; color: #28a745;"></i>
                                        Code chính xác
                                    </h5>
                                    <pre style="background: #e8f5e8; padding: 15px; border-radius: 8px; overflow-x: auto; font-family: 'Courier New', monospace; font-size: 13px; margin: 0; border: 1px solid #c3e6cb; line-height: 1.5; color: #155724;">${getCorrectCode(i)}</pre>
                                </div>
                            </div>

                            <!-- So sánh kết quả -->
                            <div style="background: white; padding: 20px; border-radius: 12px; margin-bottom: 15px; border: 1px solid #e0e0e0; box-shadow: 0 2px 8px rgba(0,0,0,0.05);">
                                <h5 style="margin: 0 0 15px 0; color: #495057; display: flex; align-items: center; font-size: 16px; font-weight: 600;">
                                    <i class="fas fa-terminal" style="margin-right: 8px; color: #6c757d;"></i>
                                    So sánh kết quả
                                </h5>
                                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                                    <div>
                                        <p style="margin: 0 0 8px 0; font-weight: 600; color: #28a745; font-size: 14px;">Output mong đợi:</p>
                                        <code style="background: #e8f5e8; padding: 12px; border-radius: 6px; display: block; font-family: 'Courier New', monospace; border: 1px solid #c3e6cb; color: #155724; font-size: 13px; line-height: 1.4; white-space: pre-wrap;">${result.expectedOutput}</code>
                                    </div>
                                    <div>
                                        <p style="margin: 0 0 8px 0; font-weight: 600; color: ${result.isCorrect ? '#28a745' : '#dc3545'}; font-size: 14px;">Output của bạn:</p>
                                        <code style="background: ${result.isCorrect ? '#e8f5e8' : '#ffeaea'}; padding: 12px; border-radius: 6px; display: block; font-family: 'Courier New', monospace; border: 1px solid ${result.isCorrect ? '#c3e6cb' : '#f5c6cb'}; color: ${result.isCorrect ? '#155724' : '#721c24'}; font-size: 13px; line-height: 1.4; white-space: pre-wrap;">${result.actualOutput || '(Không có output)'}</code>
                                    </div>
                                </div>
                            </div>

                            <!-- Giải thích Code -->
                            <div style="background: #f0f8ff; padding: 20px; border-radius: 12px; border-left: 4px solid #007bff; box-shadow: 0 2px 8px rgba(0,0,0,0.05);">
                                <h6 style="margin: 0 0 12px 0; color: #007bff; display: flex; align-items: center; font-size: 15px; font-weight: 600;">
                                    <i class="fas fa-info-circle" style="margin-right: 8px;"></i>
                                    Giải thích Code
                                </h6>
                                <div style="margin: 0; color: #333; font-size: 14px; line-height: 1.6;">${getExplanation(i)}</div>
                            </div>
                        ` : isSkipped ? `
                            <div style="background: #fff3cd; padding: 20px; border-radius: 12px; border: 1px solid #ffeaa7; text-align: center;">
                                <i class="fas fa-exclamation-triangle" style="font-size: 32px; color: #856404; margin-bottom: 15px;"></i>
                                <p style="margin: 0 0 10px 0; color: #856404; font-weight: bold; font-size: 16px;">Bài này đã được bỏ qua</p>
                                <p style="margin: 0 0 20px 0; color: #856404; font-size: 14px;">Bạn có thể xem code mẫu và giải thích bên dưới:</p>

                                <div style="background: white; padding: 20px; border-radius: 10px; margin-top: 15px; text-align: left;">
                                    <!-- Đề bài -->
                                    <div style="margin-bottom: 20px;">
                                        <h6 style="margin: 0 0 10px 0; color: #495057; font-size: 15px; font-weight: 600;">Đề bài:</h6>
                                        <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; border: 1px solid #e9ecef; color: #495057; font-size: 14px; line-height: 1.6;">
                                            ${getProblemDescription(i)}
                                        </div>
                                    </div>

                                    <!-- Output cần có -->
                                    <div style="margin-bottom: 20px;">
                                        <h6 style="margin: 0 0 10px 0; color: #495057; font-size: 15px; font-weight: 600;">Output cần có:</h6>
                                        <pre style="background: #e8f5e8; padding: 15px; border-radius: 8px; overflow-x: auto; font-family: 'Courier New', monospace; font-size: 13px; margin: 0; border: 1px solid #c3e6cb; line-height: 1.5; color: #155724; white-space: pre-wrap;">${getExpectedOutput(i)}</pre>
                                    </div>

                                    <!-- Code chính xác -->
                                    <div style="margin-bottom: 20px;">
                                        <h6 style="margin: 0 0 10px 0; color: #495057; font-size: 15px; font-weight: 600;">Code chính xác:</h6>
                                        <pre style="background: #e8f5e8; padding: 15px; border-radius: 8px; overflow-x: auto; font-family: 'Courier New', monospace; font-size: 13px; margin: 0; border: 1px solid #c3e6cb; color: #155724;">${getCorrectCode(i)}</pre>
                                    </div>

                                    <!-- Giải thích Code -->
                                    <div style="background: #f0f8ff; padding: 15px; border-radius: 8px; border-left: 4px solid #007bff;">
                                        <h6 style="margin: 0 0 10px 0; color: #007bff; font-size: 14px; font-weight: 600;">Giải thích Code:</h6>
                                        <div style="margin: 0; color: #333; font-size: 14px; line-height: 1.5;">${getExplanation(i)}</div>
                                    </div>
                                </div>
                            </div>
                        ` : `
                            <div style="background: #f8f9fa; padding: 30px; border-radius: 12px; border: 1px solid #dee2e6; text-align: center;">
                                <i class="fas fa-clock" style="font-size: 32px; color: #6c757d; margin-bottom: 15px;"></i>
                                <p style="margin: 0; color: #6c757d; font-weight: bold; font-size: 16px;">Bài này chưa được làm</p>
                                <p style="margin: 5px 0 0 0; color: #6c757d; font-size: 14px;">Hãy hoàn thành bài tập để xem kết quả</p>
                            </div>
                        `}
                    </div>
                `;
            }

            document.getElementById('detailedResults').innerHTML = detailedHTML;
        }

        // Get problem title by number
        function getProblemTitle(problemNumber) {
            const titles = {
                1: 'Lời Chào Tiêu Chuẩn',
                2: 'Thông Tin Học Viên',
                3: 'Ghi Chú Đúng Cách',
                4: 'In Chuỗi Đặc Biệt',
                5: 'Bài Thơ Con Cóc'
            };
            return titles[problemNumber] || `Bài ${problemNumber}`;
        }

        // Get problem description
        function getProblemDescription(problemNumber) {
            const descriptions = {
                1: `Tên file: loi_chao.py<br>Yêu cầu: Viết một chương trình Python để in ra màn hình chính xác dòng chữ sau:`,
                2: `Tên file: thong_tin_hoc_vien.py<br>Yêu cầu: Viết một chương trình Python để in ra màn hình chính xác 3 dòng thông tin sau đây.`,
                3: `Tên file: ghi_chu_code.py<br>Yêu cầu: Viết một chương trình Python thực hiện các yêu cầu sau:<br>- Dòng đầu tiên của file phải là một comment ghi tên của bạn, ví dụ: # Nguyen Van B.<br>- Dòng thứ hai in ra màn hình chính xác dòng chữ: Lap trinh Python that thu vi!<br>- Dòng thứ ba là một comment giải thích mục đích của dòng lệnh print ở trên.`,
                4: `Tên file: chuoi_dac_biet.py<br>Yêu cầu: Viết một chương trình Python để in ra màn hình chính xác chuỗi ký tự sau, bao gồm cả các dấu ngoặc kép và dấu chấm than.`,
                5: `Tên file: bai_tho.py<br>Yêu cầu: Viết một chương trình Python để in ra màn hình chính xác 4 dòng của một "bài thơ con cóc" sau đây. Mỗi dòng thơ phải được in trên một dòng riêng biệt.`
            };
            return descriptions[problemNumber] || `Mô tả cho bài ${problemNumber}`;
        }

        // Get expected output
        function getExpectedOutput(problemNumber) {
            const outputs = {
                1: `Chao mung den voi khoa hoc Python va AI!`,
                2: `Ho va ten: Nguyen Van An
Lop: Python Co Ban
Muc tieu: Tro thanh lap trinh vien Python.`,
                3: `Lap trinh Python that thu vi!`,
                4: `Python noi: "Hello, World!" Lap trinh rat vui!!!`,
                5: `Dong code dau tien,
Python chay rat em.
Man hinh hien loi chao,
Long vui sao la vui!`
            };
            return outputs[problemNumber] || `Output cho bài ${problemNumber}`;
        }

        // Get correct code for each problem
        function getCorrectCode(problemNumber) {
            const correctCodes = {
                1: `print("Chao mung den voi khoa hoc Python va AI!")`,
                2: `print("Ho va ten: Nguyen Van An")
print("Lop: Python Co Ban")
print("Muc tieu: Tro thanh lap trinh vien Python.")`,
                3: `# Nguyen Van B
print("Lap trinh Python that thu vi!")
# Dong lenh tren dung de in ra mot thong diep.`,
                4: `print('Python noi: "Hello, World!" Lap trinh rat vui!!!')`,
                5: `print("Dong code dau tien,")
print("Python chay rat em.")
print("Man hinh hien loi chao,")
print("Long vui sao la vui!")`
            };
            return correctCodes[problemNumber] || `# Code mẫu cho bài ${problemNumber}`;
        }

        // Get explanation for each problem
        function getExplanation(problemNumber) {
            const explanations = {
                1: `Sử dụng hàm print() để in ra chuỗi văn bản. Chuỗi được đặt trong dấu ngoặc kép và phải chính xác theo yêu cầu.`,
                2: `Sử dụng 3 lệnh print() riêng biệt để in ra 3 dòng thông tin. Mỗi lệnh print() sẽ in một dòng và tự động xuống dòng.`,
                3: `- Dòng đầu tiên là comment bắt đầu bằng dấu # để ghi tên<br>- Dòng thứ hai là lệnh print() để in ra thông điệp<br>- Dòng thứ ba là comment giải thích mục đích của lệnh print<br>- Comment không được thực thi, chỉ để ghi chú.`,
                4: `Sử dụng dấu ngoặc đơn (') để bao quanh chuỗi chứa dấu ngoặc kép ("). Điều này giúp Python phân biệt được dấu ngoặc kép là nội dung cần in ra chứ không phải ký tự kết thúc chuỗi.`,
                5: `Sử dụng 4 lệnh print() riêng biệt để in ra 4 dòng thơ. Mỗi lệnh print() sẽ in một dòng thơ và tự động xuống dòng cho dòng tiếp theo.`
            };
            return explanations[problemNumber] || `Giải thích cho bài ${problemNumber}`;
        }

        // Make functions global
        window.showProblem = showProblem;
        window.submitCode = submitCode;
        window.skipProblem = skipProblem;
        window.resetCode = resetCode;
    </script>

    <script src="../../../assets/js/script.js"></script>
</body>
</html>
