<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>17 <PERSON><PERSON><PERSON>ậ<PERSON> T<PERSON>p - Bài 3: <PERSON><PERSON> <PERSON><PERSON>, <PERSON><PERSON><PERSON> và Chuỗi</title>
    <link rel="stylesheet" href="../../../assets/css/styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        .assignment-container {
            max-width: 1000px;
            margin: 120px auto 50px;
            padding: 0 20px;
        }
        
        .assignment-header {
            text-align: center;
            margin-bottom: 40px;
            padding: 30px;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border-radius: 15px;
        }
        
        .assignment-header h1 {
            font-size: 2rem;
            margin-bottom: 15px;
        }
        
        .assignment-header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }
        
        .back-link {
            display: inline-block;
            color: #4285F4;
            text-decoration: none;
            margin-bottom: 20px;
            font-weight: 500;
        }
        
        .back-link:hover {
            text-decoration: underline;
        }

        .exercises-container {
            margin-top: 30px;
        }

        .exercise-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            margin-bottom: 25px;
            overflow: hidden;
            transition: transform 0.3s ease;
        }

        .exercise-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .exercise-header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .exercise-header h3 {
            margin: 0;
            font-size: 1.3rem;
        }

        .difficulty {
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 600;
        }

        .difficulty.easy {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .difficulty.medium {
            background: rgba(255, 193, 7, 0.2);
            color: #ffc107;
            border: 1px solid #ffc107;
        }

        .difficulty.hard {
            background: rgba(220, 53, 69, 0.2);
            color: #dc3545;
            border: 1px solid #dc3545;
        }

        .exercise-content {
            padding: 25px;
        }

        .exercise-content p {
            margin-bottom: 15px;
            line-height: 1.6;
        }

        .exercise-content ul {
            margin: 15px 0;
            padding-left: 25px;
        }

        .exercise-content li {
            margin-bottom: 8px;
            line-height: 1.6;
        }

        .output-section {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #28a745;
        }

        .output-section h4 {
            margin: 0 0 10px 0;
            color: #333;
            font-size: 1rem;
        }

        .output-box {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 0.95rem;
            line-height: 1.4;
            margin-top: 10px;
        }

        .hint-section {
            margin-top: 15px;
            padding: 15px;
            background: #fff3cd;
            border-radius: 8px;
            border-left: 4px solid #ffc107;
        }

        .hint-section h4 {
            margin: 0 0 10px 0;
            color: #856404;
            font-size: 1rem;
        }

        .hint-section p {
            margin: 0;
            color: #856404;
            font-style: italic;
        }

        .info-box {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 30px;
            text-align: center;
        }

        .info-box h3 {
            color: #0c5460;
            margin-bottom: 10px;
        }

        .info-box p {
            color: #0c5460;
            margin: 0;
        }
    </style>
</head>
<body>
    <!-- Navigation Bar -->
    <header>
        <div class="container">
            <div class="logo">
                <img src="../../../assets/images/logo.jpg" alt="VTA Logo">
            </div>
            <nav>
                <ul>
                    <li><a href="../../../index.html">Trang Chủ</a></li>
                    <li><a href="../../index.html">Lớp Học</a></li>
                    <li><a href="../../../achievements/">Thành Tích</a></li>
                    <li><a href="../../../auth/register.html">Đăng Ký</a></li>
                    <li><a href="../../../rankings/">Bảng Xếp Hạng</a></li>
                    <li><a href="../../../research/">Nghiên Cứu</a></li>
                    <li><a href="../../../auth/">Tài Khoản</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <div class="assignment-container">
        <a href="../../python-a.html" class="back-link">
            <i class="fas fa-arrow-left"></i> Quay lại lớp Python - A
        </a>

        <div class="assignment-header">
            <h1><i class="fas fa-code"></i> 17 Bài Tập Luyện Tập</h1>
            <p>Bài 3: Dữ Liệu – Biến, Kiểu Số và Chuỗi</p>
        </div>

        <div class="info-box">
            <h3><i class="fas fa-info-circle"></i> Hướng Dẫn</h3>
            <p>💡 Các bài tập này giúp bạn luyện tập kiến thức về biến, số và chuỗi. Hãy thử làm và so sánh với output mong muốn! <strong>Không tính điểm.</strong></p>
        </div>

        <!-- Exercise List -->
        <div class="exercises-container">
            <!-- Bài 1 -->
            <div class="exercise-card">
                <div class="exercise-header">
                    <h3><i class="fas fa-code"></i> Bài 1: Khai Báo Tuổi</h3>
                    <span class="difficulty easy">Dễ</span>
                </div>
                <div class="exercise-content">
                    <p><strong>Tên file:</strong> khai_bao_tuoi.py</p>
                    <p><strong>Yêu cầu:</strong> Tạo một biến tên là my_age và gán cho nó giá trị là tuổi hiện tại của bạn (một số nguyên). Sau đó, in giá trị của biến my_age ra màn hình.</p>
                    <div class="output-section">
                        <h4>Output Mẫu (Ví dụ nếu tuổi là 15):</h4>
                        <div class="output-box">15</div>
                    </div>
                </div>
            </div>

            <!-- Bài 2 -->
            <div class="exercise-card">
                <div class="exercise-header">
                    <h3><i class="fas fa-code"></i> Bài 2: Lời Chào Với Tên</h3>
                    <span class="difficulty easy">Dễ</span>
                </div>
                <div class="exercise-content">
                    <p><strong>Tên file:</strong> chao_ten.py</p>
                    <p><strong>Yêu cầu:</strong> Tạo một biến user_name và gán cho nó một tên bất kỳ (ví dụ: "Linh"). Sau đó, in ra màn hình lời chào: "Chao ban, [user_name]!" (thay [user_name] bằng giá trị của biến).</p>
                    <div class="output-section">
                        <h4>Output Mẫu (Ví dụ nếu user_name là "Linh"):</h4>
                        <div class="output-box">Chao ban, Linh!</div>
                    </div>
                </div>
            </div>

            <!-- Bài 3 -->
            <div class="exercise-card">
                <div class="exercise-header">
                    <h3><i class="fas fa-code"></i> Bài 3: Tính Diện Tích Hình Chữ Nhật Đơn Giản</h3>
                    <span class="difficulty easy">Dễ</span>
                </div>
                <div class="exercise-content">
                    <p><strong>Tên file:</strong> dien_tich_hcn.py</p>
                    <p><strong>Yêu cầu:</strong></p>
                    <ul>
                        <li>Tạo biến chieu_dai và gán giá trị là 10.</li>
                        <li>Tạo biến chieu_rong và gán giá trị là 5.</li>
                        <li>Tạo biến dien_tich và gán cho nó kết quả của chieu_dai nhân chieu_rong (dấu nhân: *).</li>
                        <li>In giá trị của biến dien_tich ra màn hình.</li>
                    </ul>
                    <div class="output-section">
                        <h4>Output Mẫu:</h4>
                        <div class="output-box">50</div>
                    </div>
                </div>
            </div>

            <!-- Bài 4 -->
            <div class="exercise-card">
                <div class="exercise-header">
                    <h3><i class="fas fa-code"></i> Bài 4: Lặp Lại Lời Cổ Vũ</h3>
                    <span class="difficulty easy">Dễ</span>
                </div>
                <div class="exercise-content">
                    <p><strong>Tên file:</strong> co_vu.py</p>
                    <p><strong>Yêu cầu:</strong> Tạo một biến slogan có giá trị là "Python Co Len! ". In biến slogan ra màn hình 3 lần liên tiếp trên cùng một dòng.</p>
                    <div class="output-section">
                        <h4>Output Mẫu:</h4>
                        <div class="output-box">Python Co Len! Python Co Len! Python Co Len! </div>
                    </div>
                </div>
            </div>

            <!-- Bài 5 -->
            <div class="exercise-card">
                <div class="exercise-header">
                    <h3><i class="fas fa-code"></i> Bài 5: Kiểm Tra Kiểu Dữ Liệu</h3>
                    <span class="difficulty easy">Dễ</span>
                </div>
                <div class="exercise-content">
                    <p><strong>Tên file:</strong> kiem_tra_kieu.py</p>
                    <p><strong>Yêu cầu:</strong></p>
                    <ul>
                        <li>Tạo biến var1 gán giá trị 100.</li>
                        <li>Tạo biến var2 gán giá trị 9.99.</li>
                        <li>Tạo biến var3 gán giá trị "Hello Python".</li>
                        <li>In ra kiểu dữ liệu của từng biến, mỗi kiểu trên một dòng.</li>
                    </ul>
                    <div class="output-section">
                        <h4>Output Mẫu:</h4>
                        <div class="output-box">&lt;class 'int'&gt;<br>&lt;class 'float'&gt;<br>&lt;class 'str'&gt;</div>
                    </div>
                </div>
            </div>

            <!-- Bài 6 -->
            <div class="exercise-card">
                <div class="exercise-header">
                    <h3><i class="fas fa-code"></i> Bài 6: Thông Tin Sản Phẩm</h3>
                    <span class="difficulty medium">Trung bình</span>
                </div>
                <div class="exercise-content">
                    <p><strong>Tên file:</strong> san_pham.py</p>
                    <p><strong>Yêu cầu:</strong></p>
                    <ul>
                        <li>Tạo biến ten_san_pham gán giá trị "Ao Thun Python".</li>
                        <li>Tạo biến gia_ban gán giá trị 250.0 (số thực).</li>
                        <li>Tạo biến so_luong_ton_kho gán giá trị 50.</li>
                        <li>In thông tin sản phẩm ra màn hình theo định dạng sau:</li>
                    </ul>
                    <div class="output-section">
                        <h4>Output Mẫu:</h4>
                        <div class="output-box">Ten san pham: Ao Thun Python<br>Gia ban: 250.0 VND<br>So luong ton kho: 50</div>
                    </div>
                    <div class="hint-section">
                        <h4>💡 Gợi ý:</h4>
                        <p>Nếu chỉ dùng print và nối chuỗi, bạn cần in gia_ban và so_luong_ton_kho riêng hoặc tìm cách nối chúng với chuỗi một cách khéo léo, ví dụ print("Gia ban: ", gia_ban, " VND") nếu đã học print nhiều tham số.</p>
                    </div>
                </div>
            </div>

            <!-- Bài 7 -->
            <div class="exercise-card">
                <div class="exercise-header">
                    <h3><i class="fas fa-code"></i> Bài 7: Tên Viết Tắt</h3>
                    <span class="difficulty medium">Trung bình</span>
                </div>
                <div class="exercise-content">
                    <p><strong>Tên file:</strong> ten_viet_tat.py</p>
                    <p><strong>Yêu cầu:</strong></p>
                    <ul>
                        <li>Tạo biến ho_dem gán giá trị "Nguyen Van".</li>
                        <li>Tạo biến ten gán giá trị "An".</li>
                        <li>Lấy ký tự đầu tiên của ho_dem và ký tự đầu tiên của ten.</li>
                        <li>Nối chúng lại với dấu chấm ở giữa để tạo tên viết tắt. In tên viết tắt đó ra màn hình.</li>
                    </ul>
                    <div class="output-section">
                        <h4>Output Mẫu:</h4>
                        <div class="output-box">N.A</div>
                    </div>
                </div>
            </div>

            <!-- Bài 8 -->
            <div class="exercise-card">
                <div class="exercise-header">
                    <h3><i class="fas fa-code"></i> Bài 8: Đảo Ngược Chuỗi Đơn Giản</h3>
                    <span class="difficulty easy">Dễ</span>
                </div>
                <div class="exercise-content">
                    <p><strong>Tên file:</strong> dao_nguoc_chuoi.py</p>
                    <p><strong>Yêu cầu:</strong> Cho biến s = "Hello". Sử dụng slicing để đảo ngược chuỗi s và in kết quả ra màn hình.</p>
                    <div class="output-section">
                        <h4>Output Mẫu:</h4>
                        <div class="output-box">olleH</div>
                    </div>
                </div>
            </div>

            <!-- Bài 9 -->
            <div class="exercise-card">
                <div class="exercise-header">
                    <h3><i class="fas fa-code"></i> Bài 9: Tạo Username</h3>
                    <span class="difficulty medium">Trung bình</span>
                </div>
                <div class="exercise-content">
                    <p><strong>Tên file:</strong> tao_username.py</p>
                    <p><strong>Yêu cầu:</strong></p>
                    <ul>
                        <li>Tạo biến first_name gán giá trị "taylor".</li>
                        <li>Tạo biến last_name gán giá trị "swift".</li>
                        <li>Tạo biến birth_year gán giá trị "1989".</li>
                        <li>Tạo username bằng cách nối 3 ký tự đầu của first_name (chuyển thành chữ thường), 3 ký tự đầu của last_name (chuyển thành chữ thường), và 2 ký tự cuối của birth_year.</li>
                        <li>In username ra màn hình.</li>
                    </ul>
                    <div class="output-section">
                        <h4>Output Mẫu:</h4>
                        <div class="output-box">tayswi89</div>
                    </div>
                </div>
            </div>

            <!-- Bài 10 -->
            <div class="exercise-card">
                <div class="exercise-header">
                    <h3><i class="fas fa-code"></i> Bài 10: Định Dạng Lại Tên</h3>
                    <span class="difficulty medium">Trung bình</span>
                </div>
                <div class="exercise-content">
                    <p><strong>Tên file:</strong> dinh_dang_ten.py</p>
                    <p><strong>Yêu cầu:</strong></p>
                    <ul>
                        <li>Tạo biến full_name có giá trị là " ngUYen vAN binh ".</li>
                        <li>Sử dụng các phương thức chuỗi để:</li>
                        <li>Loại bỏ khoảng trắng thừa ở đầu và cuối.</li>
                        <li>Chuyển toàn bộ tên thành chữ thường.</li>
                        <li>Sau đó, chuyển ký tự đầu tiên của mỗi từ (sau khi đã xử lý ở trên) thành chữ hoa</li>
                        <li>In tên đã định dạng ra màn hình.</li>
                    </ul>
                    <div class="output-section">
                        <h4>Output Mẫu:</h4>
                        <div class="output-box">Nguyen Van Binh</div>
                    </div>
                    <div class="hint-section">
                        <h4>💡 Gợi ý:</h4>
                        <p>Nếu chưa học title(), bạn có thể làm như sau: strip(), lower(), sau đó lấy name[0].upper() + name[1:] cho từng từ sau khi tách chuỗi, hoặc làm phức tạp hơn bằng cách tìm khoảng trắng để xác định đầu từ.</p>
                    </div>
                </div>
            </div>

            <!-- Bài 11 -->
            <div class="exercise-card">
                <div class="exercise-header">
                    <h3><i class="fas fa-code"></i> Bài 11: Tạo Email Đơn Giản</h3>
                    <span class="difficulty medium">Trung bình</span>
                </div>
                <div class="exercise-content">
                    <p><strong>Tên file:</strong> tao_email.py</p>
                    <p><strong>Yêu cầu:</strong></p>
                    <ul>
                        <li>Tạo biến ho_va_ten gán giá trị "Tran Minh Hoang".</li>
                        <li>Tạo biến ten_mien_cong_ty gán giá trị "techcompany.com".</li>
                        <li>Tạo địa chỉ email theo quy tắc: lấy chữ cái đầu tiên của mỗi từ trong ho_va_ten (chuyển thành chữ thường), nối chúng lại, sau đó nối với @ và ten_mien_cong_ty.</li>
                        <li>In địa chỉ email ra màn hình.</li>
                    </ul>
                    <div class="output-section">
                        <h4>Output Mẫu:</h4>
                        <div class="output-box"><EMAIL></div>
                    </div>
                    <div class="hint-section">
                        <h4>💡 Gợi ý:</h4>
                        <p>Học sinh cần dùng indexing và nối chuỗi. Có thể giả sử tên có 3 từ để đơn giản hóa việc lấy ký tự đầu.</p>
                    </div>
                </div>
            </div>

            <!-- Bài 12 -->
            <div class="exercise-card">
                <div class="exercise-header">
                    <h3><i class="fas fa-code"></i> Bài 12: Trích Xuất Thông Tin Từ Chuỗi</h3>
                    <span class="difficulty hard">Khó</span>
                </div>
                <div class="exercise-content">
                    <p><strong>Tên file:</strong> trich_xuat_thong_tin.py</p>
                    <p><strong>Yêu cầu:</strong> Cho biến log_entry = "INFO:UserID:12345:Action:ViewPage".</p>
                    <ul>
                        <li>Sử dụng slicing và/hoặc find() để trích xuất UserID (là "12345").</li>
                        <li>Sử dụng slicing và/hoặc find() để trích xuất Action (là "ViewPage").</li>
                        <li>In UserID và Action ra màn hình, mỗi thông tin trên một dòng.</li>
                    </ul>
                    <div class="output-section">
                        <h4>Output Mẫu:</h4>
                        <div class="output-box">UserID: 12345<br>Action: ViewPage</div>
                    </div>
                    <div class="hint-section">
                        <h4>💡 Gợi ý:</h4>
                        <p>Đây là bài khó, học sinh cần tìm vị trí của các dấu : để cắt chuỗi.</p>
                    </div>
                </div>
            </div>

            <!-- Bài 13 -->
            <div class="exercise-card">
                <div class="exercise-header">
                    <h3><i class="fas fa-code"></i> Bài 13: Tạo Khung Văn Bản</h3>
                    <span class="difficulty medium">Trung bình</span>
                </div>
                <div class="exercise-content">
                    <p><strong>Tên file:</strong> khung_van_ban.py</p>
                    <p><strong>Yêu cầu:</strong></p>
                    <ul>
                        <li>Tạo biến message gán giá trị "Python is Awesome".</li>
                        <li>In ra một khung bao quanh message bằng các ký tự * và - như sau:</li>
                    </ul>
                    <div class="output-section">
                        <h4>Output Mẫu:</h4>
                        <div class="output-box">********************<br>* Python is Awesome *<br>********************</div>
                    </div>
                    <div class="hint-section">
                        <h4>💡 Gợi ý:</h4>
                        <p>Học sinh cần tính độ dài của message, sau đó tạo các dòng * có độ dài phù hợp. Dòng giữa cần có *, khoảng trắng, message, khoảng trắng, *.</p>
                    </div>
                </div>
            </div>

            <!-- Bài 14 -->
            <div class="exercise-card">
                <div class="exercise-header">
                    <h3><i class="fas fa-code"></i> Bài 14: Mã Hóa Caesar Đơn Giản (Dịch Vòng)</h3>
                    <span class="difficulty hard">Khó</span>
                </div>
                <div class="exercise-content">
                    <p><strong>Tên file:</strong> ma_hoa_caesar.py</p>
                    <p><strong>Yêu cầu:</strong></p>
                    <ul>
                        <li>Tạo biến plain_text gán giá trị "HELLO".</li>
                        <li>Tạo biến shift gán giá trị 3.</li>
                        <li>Với mỗi ký tự trong plain_text, dịch chuyển nó đi shift vị trí trong bảng chữ cái (ví dụ: A dịch 3 thành D, B dịch 3 thành E,... Z dịch 3 thành C).</li>
                        <li>Chỉ xử lý chữ cái IN HOA, các ký tự khác giữ nguyên (cho bài này, giả sử chuỗi đầu vào chỉ có chữ IN HOA).</li>
                        <li>In ra chuỗi đã mã hóa.</li>
                    </ul>
                    <div class="output-section">
                        <h4>Output Mẫu:</h4>
                        <div class="output-box">KHOOR</div>
                    </div>
                    <div class="hint-section">
                        <h4>💡 Gợi ý:</h4>
                        <p>Bài này rất khó nếu chỉ dùng kiến thức Bài 3. Học sinh có thể tạo một chuỗi alphabet = "ABCDEFGHIJKLMNOPQRSTUVWXYZ". Với mỗi ký tự trong plain_text, tìm vị trí của nó trong alphabet, cộng với shift, rồi lấy ký tự ở vị trí mới (có xử lý vòng lặp nếu vượt quá Z).</p>
                    </div>
                </div>
            </div>

            <!-- Bài 15 -->
            <div class="exercise-card">
                <div class="exercise-header">
                    <h3><i class="fas fa-code"></i> Bài 15: Tạo Mật Khẩu Ngẫu Nhiên Giả (Fixed)</h3>
                    <span class="difficulty medium">Trung bình</span>
                </div>
                <div class="exercise-content">
                    <p><strong>Tên file:</strong> mat_khau_gia.py</p>
                    <p><strong>Yêu cầu:</strong></p>
                    <ul>
                        <li>Tạo biến name_part gán giá trị "User".</li>
                        <li>Tạo biến number_part gán giá trị "2024". (Chuỗi)</li>
                        <li>Tạo biến symbol_part gán giá trị "@#!".</li>
                        <li>Tạo mật khẩu bằng cách: lấy 2 ký tự đầu của name_part + number_part + ký tự thứ hai của symbol_part + 3 ký tự cuối của name_part đảo ngược.</li>
                        <li>In mật khẩu ra màn hình.</li>
                    </ul>
                    <div class="output-section">
                        <h4>Output Mẫu (Với "User" và "2024"):</h4>
                        <div class="output-box">Us2024#res</div>
                    </div>
                    <div class="hint-section">
                        <h4>💡 Gợi ý:</h4>
                        <p>Để học sinh làm được với kiến thức Bài 3: Lấy 2 ký tự đầu name_part[0:2]. Ký tự thứ hai của symbol_part là symbol_part[1]. 3 ký tự cuối của name_part đảo ngược: name_part[3] + name_part[2] + name_part[1] nếu name_part là "User".</p>
                    </div>
                </div>
            </div>

            <!-- Bài 16 -->
            <div class="exercise-card">
                <div class="exercise-header">
                    <h3><i class="fas fa-code"></i> Bài 16: Format Địa Chỉ</h3>
                    <span class="difficulty easy">Dễ</span>
                </div>
                <div class="exercise-content">
                    <p><strong>Tên file:</strong> format_dia_chi.py</p>
                    <p><strong>Yêu cầu:</strong></p>
                    <ul>
                        <li>Tạo biến so_nha gán giá trị "123B".</li>
                        <li>Tạo biến ten_duong gán giá trị "Duong Tran Hung Dao".</li>
                        <li>Tạo biến phuong gán giá trị "Phuong Pham Ngu Lao".</li>
                        <li>Tạo biến quan gán giá trị "Quan 1".</li>
                        <li>Tạo biến thanh_pho gán giá trị "TP. Ho Chi Minh".</li>
                        <li>In ra địa chỉ đầy đủ trên một dòng, mỗi phần cách nhau bởi dấu phẩy và một khoảng trắng.</li>
                    </ul>
                    <div class="output-section">
                        <h4>Output Mẫu:</h4>
                        <div class="output-box">123B, Duong Tran Hung Dao, Phuong Pham Ngu Lao, Quan 1, TP. Ho Chi Minh</div>
                    </div>
                </div>
            </div>

            <!-- Bài 17 -->
            <div class="exercise-card">
                <div class="exercise-header">
                    <h3><i class="fas fa-code"></i> Bài 17: Phân Tích Chuỗi Đơn Giản</h3>
                    <span class="difficulty hard">Khó</span>
                </div>
                <div class="exercise-content">
                    <p><strong>Tên file:</strong> phan_tich_chuoi.py</p>
                    <p><strong>Yêu cầu:</strong></p>
                    <ul>
                        <li>Tạo biến my_string gán giá trị "Python:123:Data:45.6".</li>
                        <li>Tìm vị trí của dấu hai chấm (:) đầu tiên.</li>
                        <li>Tìm vị trí của dấu hai chấm (:) thứ hai (tính từ sau vị trí đầu tiên).</li>
                        <li>Tìm vị trí của dấu hai chấm (:) cuối cùng.</li>
                        <li>In ra ba vị trí này, mỗi vị trí trên một dòng.</li>
                    </ul>
                    <div class="output-section">
                        <h4>Output Mẫu:</h4>
                        <div class="output-box">Vi tri dau hai cham dau tien: 6<br>Vi tri dau hai cham thu hai: 10<br>Vi tri dau hai cham cuoi cung: 15</div>
                    </div>
                    <div class="hint-section">
                        <h4>💡 Gợi ý:</h4>
                        <p>Phương thức find() có tham số start để bắt đầu tìm kiếm từ một vị trí nhất định. Ví dụ: my_string.find(":", vi_tri_dau_tien + 1)</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-section footer-logo">
                    <div class="footer-logo-container">
                        <img src="../../../assets/images/logo.jpg" alt="VTA Logo">
                        <div class="logo-text">Vthon Academy</div>
                    </div>
                    <div class="slogan">Học, học nữa, học mãi.</div>
                    <div class="footer-bottom">
                        <p>&copy; 2025 – All rights reserved.</p>
                    </div>
                </div>

                <div class="footer-section footer-contact">
                    <h3>Liên hệ</h3>
                    <p><i class="fas fa-envelope"></i> <EMAIL></p>
                    <p><i class="fas fa-phone"></i> 0399787678</p>
                    <p><i class="fab fa-facebook-messenger"></i> Zalo: 0399787678</p>
                </div>

                <div class="footer-section footer-social">
                    <h3>Mạng xã hội</h3>
                    <div class="social-links">
                        <a href="https://www.facebook.com/vinhle030904/" target="_blank" class="social-link facebook">
                            <i class="fab fa-facebook-f"></i> Facebook
                        </a>
                        <a href="https://www.tiktok.com/@sunnii39" target="_blank" class="social-link tiktok">
                            <i class="fab fa-tiktok"></i> TikTok
                        </a>
                        <a href="https://www.instagram.com/vlee.39/?hl=en" target="_blank" class="social-link instagram">
                            <i class="fab fa-instagram"></i> Instagram
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <script src="../../../assets/js/script.js"></script>
</body>
</html>
