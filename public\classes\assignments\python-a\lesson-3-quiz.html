<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON>ập <PERSON><PERSON>ài 3: <PERSON><PERSON> <PERSON><PERSON>, <PERSON><PERSON><PERSON> v<PERSON> Chuỗi</title>
    <link rel="stylesheet" href="../../../assets/css/styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        .assignment-container {
            max-width: 900px;
            margin: 120px auto 50px;
            padding: 0 20px;
        }
        
        .assignment-header {
            text-align: center;
            margin-bottom: 40px;
            padding: 30px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
        }
        
        .assignment-header h1 {
            font-size: 2rem;
            margin-bottom: 15px;
        }
        
        .assignment-header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }
        
        .quiz-info {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .quiz-info h2 {
            color: #4285F4;
            margin-bottom: 20px;
        }
        
        .quiz-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .stat-item {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .stat-item i {
            font-size: 2rem;
            color: #667eea;
            margin-bottom: 10px;
        }
        
        .stat-item h3 {
            margin: 10px 0 5px 0;
            color: #333;
        }
        
        .stat-item p {
            margin: 0;
            color: #666;
            font-size: 0.9rem;
        }
        
        .start-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 40px;
            border-radius: 50px;
            font-size: 1.2rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 20px 0;
        }
        
        .start-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
        }
        
        .back-link {
            display: inline-block;
            color: #4285F4;
            text-decoration: none;
            margin-bottom: 20px;
            font-weight: 500;
        }
        
        .back-link:hover {
            text-decoration: underline;
        }
        
        .coming-soon {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 10px;
            padding: 30px;
            text-align: center;
            margin: 30px 0;
        }
        
        .coming-soon h3 {
            color: #856404;
            margin-bottom: 15px;
        }
        
        .coming-soon p {
            color: #856404;
            margin: 0;
        }
    </style>
</head>
<body>
    <!-- Navigation Bar -->
    <header>
        <div class="container">
            <div class="logo">
                <img src="../../../assets/images/logo.jpg" alt="VTA Logo">
            </div>
            <nav>
                <ul>
                    <li><a href="../../../index.html">Trang Chủ</a></li>
                    <li><a href="../../index.html">Lớp Học</a></li>
                    <li><a href="../../../achievements/">Thành Tích</a></li>
                    <li><a href="../../../auth/register.html">Đăng Ký</a></li>
                    <li><a href="../../../rankings/">Bảng Xếp Hạng</a></li>
                    <li><a href="../../../research/">Nghiên Cứu</a></li>
                    <li><a href="../../../auth/">Tài Khoản</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <div class="assignment-container">
        <a href="../../lessons/python-a/lesson-3.html" class="back-link">
            <i class="fas fa-arrow-left"></i> Quay lại Bài 3
        </a>

        <div class="assignment-header">
            <h1><i class="fas fa-question-circle"></i> Bài Tập Trắc Nghiệm</h1>
            <p>Bài 3: Dữ Liệu – Biến, Kiểu Số và Chuỗi</p>
        </div>

        <div class="quiz-info">
            <h2>Thông Tin Bài Tập</h2>
            <div class="quiz-stats">
                <div class="stat-item">
                    <i class="fas fa-list-ol"></i>
                    <h3>50 Câu Hỏi</h3>
                    <p>Trắc nghiệm 4 đáp án</p>
                </div>
                <div class="stat-item">
                    <i class="fas fa-clock"></i>
                    <h3>75 Phút</h3>
                    <p>Thời gian làm bài</p>
                </div>
                <div class="stat-item">
                    <i class="fas fa-chart-line"></i>
                    <h3>50 Điểm</h3>
                    <p>50% tổng điểm</p>
                </div>
                <div class="stat-item">
                    <i class="fas fa-redo"></i>
                    <h3>1 Lần</h3>
                    <p>Số lần làm bài</p>
                </div>
            </div>
            
            <div class="coming-soon">
                <h3><i class="fas fa-tools"></i> Đang Phát Triển</h3>
                <p>Bài tập trắc nghiệm đang được chuẩn bị. Vui lòng quay lại sau!</p>
            </div>
            
            <!-- Uncomment when quiz is ready -->
            <!--
            <button class="start-button" onclick="startQuiz()">
                <i class="fas fa-play"></i> Bắt Đầu Làm Bài
            </button>
            -->
        </div>
    </div>

    <!-- Footer -->
    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-section footer-logo">
                    <div class="footer-logo-container">
                        <img src="../../../assets/images/logo.jpg" alt="VTA Logo">
                        <div class="logo-text">Vthon Academy</div>
                    </div>
                    <div class="slogan">Học, học nữa, học mãi.</div>
                    <div class="footer-bottom">
                        <p>&copy; 2025 – All rights reserved.</p>
                    </div>
                </div>

                <div class="footer-section footer-contact">
                    <h3>Liên hệ</h3>
                    <p><i class="fas fa-envelope"></i> <EMAIL></p>
                    <p><i class="fas fa-phone"></i> 0399787678</p>
                    <p><i class="fab fa-facebook-messenger"></i> Zalo: 0399787678</p>
                </div>

                <div class="footer-section footer-social">
                    <h3>Mạng xã hội</h3>
                    <div class="social-links">
                        <a href="https://www.facebook.com/vinhle030904/" target="_blank" class="social-link facebook">
                            <i class="fab fa-facebook-f"></i> Facebook
                        </a>
                        <a href="https://www.tiktok.com/@sunnii39" target="_blank" class="social-link tiktok">
                            <i class="fab fa-tiktok"></i> TikTok
                        </a>
                        <a href="https://www.instagram.com/vlee.39/?hl=en" target="_blank" class="social-link instagram">
                            <i class="fab fa-instagram"></i> Instagram
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <script>
        function startQuiz() {
            // This will be implemented when quiz content is ready
            alert('Bài tập sẽ sớm được cập nhật!');
        }
    </script>
    <script src="../../../assets/js/script.js"></script>
</body>
</html>
