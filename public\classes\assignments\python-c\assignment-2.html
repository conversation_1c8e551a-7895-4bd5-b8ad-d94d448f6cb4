<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bài Tập 2: Python và Cài Đặt Môi Trường - Python A</title>
    <link rel="stylesheet" href="../../../assets/css/styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        .assignment-container {
            max-width: 1000px;
            margin: 120px auto 50px;
            padding: 0 20px;
        }
        
        .assignment-header {
            text-align: center;
            margin-bottom: 30px;
            padding: 25px;
            background: linear-gradient(135deg, #4285F4, #ff7aa8);
            color: white;
            border-radius: 15px;
        }
        
        .assignment-header h1 {
            font-size: 2rem;
            margin-bottom: 10px;
        }
        
        .timer-info {
            background: white;
            color: #333;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
        }
        
        .timer {
            font-size: 1.5rem;
            font-weight: bold;
            color: #4285F4;
        }
        
        .quiz-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
            padding: 30px;
            margin-bottom: 30px;
        }
        
        .question {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 10px;
            display: none;
        }
        
        .question.active {
            display: block;
        }
        
        .question-number {
            color: #4285F4;
            font-weight: bold;
            margin-bottom: 15px;
        }
        
        .question-text {
            font-size: 1.1rem;
            margin-bottom: 20px;
            line-height: 1.6;
        }
        
        .options {
            list-style: none;
            padding: 0;
        }
        
        .option {
            margin-bottom: 10px;
            padding: 15px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .option:hover {
            border-color: #4285F4;
            background-color: #f8f9fa;
        }
        
        .option.selected {
            border-color: #4285F4;
            background-color: #e3f2fd;
        }
        
        .option input[type="radio"] {
            margin-right: 10px;
        }
        
        .navigation {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 30px;
            padding: 20px 0;
        }
        
        .nav-btn {
            background: #4285F4;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
            transition: background-color 0.3s;
        }
        
        .nav-btn:hover {
            background: #3367D6;
        }
        
        .nav-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e0e0e0;
            border-radius: 4px;
            margin: 20px 0;
        }
        
        .progress-fill {
            height: 100%;
            background: #4285F4;
            border-radius: 4px;
            transition: width 0.3s;
        }
        
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(40px, 1fr));
            gap: 10px;
            margin: 20px 0;
        }
        
        .question-indicator {
            width: 40px;
            height: 40px;
            border: 2px solid #e0e0e0;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s;
            font-weight: bold;
        }
        
        .question-indicator.answered {
            background: #4285F4;
            color: white;
            border-color: #4285F4;
        }
        
        .question-indicator.current {
            border-color: #ff7aa8;
            background: #ff7aa8;
            color: white;
        }
        
        .submit-section {
            text-align: center;
            margin-top: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
        }
        
        .submit-btn {
            background: #28a745;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-size: 1.1rem;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        
        .submit-btn:hover {
            background: #218838;
        }
        
        .back-link {
            display: inline-block;
            color: #4285F4;
            text-decoration: none;
            margin-bottom: 20px;
            font-weight: 500;
        }
        
        .back-link:hover {
            text-decoration: underline;
        }
        
        .results-container {
            display: none;
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
            padding: 30px;
            margin-bottom: 30px;
        }
        
        .score-display {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
        }
        
        .score-number {
            font-size: 3rem;
            font-weight: bold;
            color: #4285F4;
        }
        
        .review-question {
            margin-bottom: 25px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 10px;
        }
        
        .correct-answer {
            color: #28a745;
            font-weight: bold;
        }
        
        .wrong-answer {
            color: #dc3545;
            font-weight: bold;
        }
        
        .explanation {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-top: 10px;
            font-style: italic;
        }
    </style>
</head>
<body>
    <!-- Navigation Bar -->
    <header>
        <div class="container">
            <div class="logo">
                <img src="../../../assets/images/logo.jpg" alt="VTA Logo">
            </div>
            <nav>
                <ul>
                    <li><a href="../../../index.html">Trang Chủ</a></li>
                    <li><a href="../../index.html">Lớp Học</a></li>
                    <li><a href="../../../achievements/">Thành Tích</a></li>
                    <li><a href="../../../auth/register.html">Đăng Ký</a></li>
                    <li><a href="../../../rankings/">Bảng Xếp Hạng</a></li>
                    <li><a href="../../../research/">Nghiên Cứu</a></li>
                    <li><a href="../../../auth/">Tài Khoản</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <div class="assignment-container">
        <a href="../../python-c.html" class="back-link">
            <i class="fas fa-arrow-left"></i> Quay lại lớp Python - C
        </a>

        <div class="assignment-header">
            <h1>Bài Tập 2: Python và Cài Đặt Môi Trường</h1>
            <p>15 câu hỏi - Thời gian: 30 phút</p>
        </div>

        <div class="timer-info">
            <div>
                <strong>Thời gian còn lại:</strong>
                <span class="timer" id="timer">Đang kiểm tra...</span>
            </div>
            <div>
                <strong>Câu hỏi:</strong>
                <span id="current-question">-</span> / <span id="total-questions">15</span>
            </div>
        </div>

        <!-- Quiz Container -->
        <div class="quiz-container" id="quizContainer">
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            
            <div class="question-grid" id="questionGrid">
                <!-- Question indicators will be generated here -->
            </div>

            <div id="questionsContainer">
                <!-- Questions will be loaded here -->
            </div>

            <div class="navigation">
                <button class="nav-btn" id="prevBtn" onclick="previousQuestion()" disabled>
                    <i class="fas fa-chevron-left"></i> Câu trước
                </button>
                
                <div>
                    <span id="questionStatus">Câu 1 / 15</span>
                </div>
                
                <button class="nav-btn" id="nextBtn" onclick="nextQuestion()">
                    Câu tiếp <i class="fas fa-chevron-right"></i>
                </button>
            </div>

            <div class="submit-section">
                <button class="submit-btn" id="submitBtn" onclick="submitQuiz()">
                    <i class="fas fa-check"></i> Nộp Bài
                </button>
            </div>
        </div>

        <!-- Results Container -->
        <div class="results-container" id="resultsContainer">
            <div class="score-display">
                <div class="score-number" id="finalScore">0</div>
                <div>điểm / 15 điểm</div>
                <div>Thời gian hoàn thành: <span id="completionTime"></span></div>
            </div>
            
            <div id="reviewContainer">
                <!-- Review will be shown here -->
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-section footer-logo">
                    <div class="footer-logo-container">
                        <img src="../../../assets/images/logo.jpg" alt="VTA Logo">
                        <div class="logo-text">Vthon Academy</div>
                    </div>
                    <div class="slogan">Học, học nữa, học mãi.</div>
                    <div class="footer-bottom">
                        <p>&copy; 2025 – All rights reserved.</p>
                    </div>
                </div>

                <div class="footer-section footer-contact">
                    <h3>Liên hệ</h3>
                    <p><i class="fas fa-envelope"></i> <EMAIL></p>
                    <p><i class="fas fa-phone"></i> 0399787678</p>
                    <p><i class="fab fa-facebook-messenger"></i> Zalo: 0399787678</p>
                </div>

                <div class="footer-section footer-social">
                    <h3>Mạng xã hội</h3>
                    <div class="social-links">
                        <a href="https://www.facebook.com/vinhle030904/" target="_blank" class="social-link facebook">
                            <i class="fab fa-facebook-f"></i> Facebook
                        </a>
                        <a href="https://www.tiktok.com/@sunnii39" target="_blank" class="social-link tiktok">
                            <i class="fab fa-tiktok"></i> TikTok
                        </a>
                        <a href="https://www.instagram.com/vlee.39/?hl=en" target="_blank" class="social-link instagram">
                            <i class="fab fa-instagram"></i> Instagram
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Firebase SDK -->
    <script type="module">
        // Import Firebase modules
        import { initializeApp } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-app.js";
        import { getAuth, onAuthStateChanged } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-auth.js";
        import { getFirestore, doc, getDoc, updateDoc, setDoc } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-firestore.js";

        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyBKNo8y_MOKYc3f3UdNRFwcMgeLRW71WXA",
            authDomain: "classroom-web-48bc2.firebaseapp.com",
            projectId: "classroom-web-48bc2",
            storageBucket: "classroom-web-48bc2.firebasestorage.app",
            messagingSenderId: "446746787502",
            appId: "1:446746787502:web:48d5ffd5a0b2c6e043b73f",
            measurementId: "G-742XRP9E96"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const auth = getAuth(app);
        const db = getFirestore(app);

        // Quiz data will be loaded here
        let quizData = [];
        let currentQuestionIndex = 0;
        let userAnswers = {};
        let timeLeft = 30 * 60; // 30 minutes in seconds
        let timerInterval;
        let startTime;

        // Initialize quiz when page loads
        window.addEventListener('DOMContentLoaded', function() {
            checkAssignmentCompletion();
        });

        // Global variables for quiz state
        let quizStarted = false;
        let quizCompleted = false;

        // Show confirmation before starting quiz
        function showQuizStartConfirmation() {
            return new Promise((resolve) => {
                // Create modal overlay
                const overlay = document.createElement('div');
                overlay.style.cssText = `
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(0, 0, 0, 0.5);
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    z-index: 10000;
                `;

                // Create modal content
                const modal = document.createElement('div');
                modal.style.cssText = `
                    background: white;
                    padding: 30px;
                    border-radius: 15px;
                    max-width: 500px;
                    text-align: center;
                    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
                `;

                modal.innerHTML = `
                    <div style="margin-bottom: 20px;">
                        <i class="fas fa-clock" style="font-size: 48px; color: #ffc107; margin-bottom: 15px;"></i>
                        <h3 style="margin: 0 0 15px 0; color: #333;">Xác nhận bắt đầu làm bài</h3>
                        <p style="margin: 0; color: #666; line-height: 1.6;">
                            Bạn chắc chắn vào làm Trắc Nghiệm chứ?<br>
                            Một khi đã vào thì sẽ bắt đầu tính giờ làm bài,<br>
                            hãy lưu ý làm theo sự hướng dẫn của Giáo Viên.
                        </p>
                    </div>
                    <div style="display: flex; gap: 15px; justify-content: center;">
                        <button id="cancelQuiz" style="
                            background: #6c757d;
                            color: white;
                            border: none;
                            padding: 12px 24px;
                            border-radius: 8px;
                            cursor: pointer;
                            font-size: 16px;
                        ">Hủy bỏ</button>
                        <button id="startQuiz" style="
                            background: #28a745;
                            color: white;
                            border: none;
                            padding: 12px 24px;
                            border-radius: 8px;
                            cursor: pointer;
                            font-size: 16px;
                        ">Bắt đầu làm bài</button>
                    </div>
                `;

                overlay.appendChild(modal);
                document.body.appendChild(overlay);

                // Handle button clicks
                document.getElementById('cancelQuiz').onclick = () => {
                    document.body.removeChild(overlay);
                    resolve(false);
                };

                document.getElementById('startQuiz').onclick = () => {
                    document.body.removeChild(overlay);
                    resolve(true);
                };
            });
        }

        // Setup anti-cheat mechanism without page unload warning
        function setupAntiCheat() {
            // Track page visibility changes
            document.addEventListener('visibilitychange', function() {
                if (quizStarted && !quizCompleted && document.hidden) {
                    console.log('User left page during quiz - marking as cheating attempt');
                    handleCheatingAttempt();
                }
            });

            // Track focus changes with delay to avoid false positives from alerts
            window.addEventListener('blur', function() {
                if (quizStarted && !quizCompleted) {
                    // Add a small delay to check if quiz is still not completed
                    // This prevents false positives when user clicks on alert dialogs
                    setTimeout(() => {
                        if (quizStarted && !quizCompleted) {
                            console.log('Window lost focus during quiz');
                            handleCheatingAttempt();
                        }
                    }, 100);
                }
            });
        }

        // Handle cheating attempt
        async function handleCheatingAttempt() {
            if (quizCompleted) return;

            quizCompleted = true;
            clearInterval(timerInterval);

            await saveZeroScore();
            showCheatingDetected();
        }

        // Save zero score when cheating detected
        async function saveZeroScore() {
            const user = auth.currentUser;
            if (!user || user.email === '<EMAIL>') return;

            try {
                // Create detailed results for all questions (all marked as incorrect)
                const results = [];
                if (quizData && quizData.length > 0) {
                    quizData.forEach((question, index) => {
                        results.push({
                            questionIndex: index,
                            question: question.question,
                            options: question.options,
                            userAnswer: -1, // No answer selected
                            correctAnswer: question.correct,
                            isCorrect: false,
                            explanation: question.explanation
                        });
                    });
                }

                const assignmentData = {
                    assignmentId: 'assignment-2',
                    assignmentTitle: 'Bài Tập 2: Python và Cài Đặt Môi Trường',
                    score: 0,
                    totalQuestions: quizData ? quizData.length : 15,
                    completionTime: 0,
                    completedAt: new Date().toISOString(),
                    results: results,
                    cheatingDetected: true,
                    reason: 'Left page during quiz'
                };

                await setDoc(doc(db, "users", user.uid, "assignments", "assignment-2"), assignmentData);

                // Update user's assignment count and total score
                const userDoc = await getDoc(doc(db, "users", user.uid));
                if (userDoc.exists()) {
                    const userData = userDoc.data();
                    await updateDoc(doc(db, "users", user.uid), {
                        assignmentCount: (userData.assignmentCount || 0) + 1,
                        totalScore: userData.totalScore || 0,
                        lastAssignmentScore: 0,
                        lastAssignmentDate: new Date().toISOString()
                    });
                }

                console.log('Zero score saved due to cheating detection');
            } catch (error) {
                console.error('Error saving zero score:', error);
            }
        }

        // Show cheating detected message
        function showCheatingDetected() {
            document.getElementById('quizContainer').style.display = 'none';
            document.getElementById('resultsContainer').style.display = 'block';

            document.querySelector('.assignment-header h1').innerHTML =
                'Bài Tập 2: Python và Cài Đặt Môi Trường <span style="color: #dc3545;">(Vi phạm quy định)</span>';

            const cheatingNotice = document.createElement('div');
            cheatingNotice.style.cssText = `
                background: linear-gradient(135deg, #dc3545, #c82333);
                color: white;
                padding: 20px;
                margin-bottom: 20px;
                border-radius: 8px;
                text-align: center;
                font-weight: bold;
                border: 2px solid #bd2130;
            `;
            cheatingNotice.innerHTML = `
                <i class="fas fa-exclamation-triangle" style="font-size: 2rem; margin-bottom: 10px;"></i>
                <h3>Vi phạm quy định làm bài!</h3>
                <p>Hệ thống đã phát hiện bạn rời khỏi trang trong quá trình làm bài.</p>
                <p><strong>Kết quả: 0 điểm - Bài tập đã được đánh dấu hoàn thành</strong></p>
                <p>Bạn có thể xem đáp án để ôn tập, nhưng không thể làm lại.</p>
                <p style="font-size: 0.9em; margin-top: 15px;">Vui lòng liên hệ giáo viên nếu đây là lỗi hệ thống.</p>
            `;

            const resultsContainer = document.getElementById('resultsContainer');
            resultsContainer.innerHTML = '';
            resultsContainer.appendChild(cheatingNotice);

            // Add view answers button
            const viewAnswersButton = document.createElement('div');
            viewAnswersButton.style.cssText = 'text-align: center; margin: 20px 0;';
            viewAnswersButton.innerHTML = `
                <button onclick="showDetailedResults()" style="
                    background: #17a2b8;
                    color: white;
                    padding: 12px 24px;
                    border: none;
                    border-radius: 8px;
                    font-weight: bold;
                    cursor: pointer;
                    margin-right: 10px;
                ">
                    <i class="fas fa-eye"></i> Xem đáp án chi tiết
                </button>
            `;
            resultsContainer.appendChild(viewAnswersButton);

            const backButton = document.createElement('div');
            backButton.style.cssText = 'text-align: center; margin-top: 10px;';
            backButton.innerHTML = `
                <a href="../../python-c.html" style="
                    display: inline-block;
                    background: #6c757d;
                    color: white;
                    padding: 12px 24px;
                    text-decoration: none;
                    border-radius: 8px;
                    font-weight: bold;
                ">
                    <i class="fas fa-arrow-left"></i> Quay lại lớp học
                </a>
            `;
            resultsContainer.appendChild(backButton);
        }

        // Show detailed results for cheating case
        function showDetailedResults() {
            if (!quizData || quizData.length === 0) {
                alert('Không thể tải dữ liệu câu hỏi. Vui lòng thử lại sau.');
                return;
            }

            const reviewContainer = document.getElementById('reviewContainer');
            let reviewHTML = '<div style="text-align: center; margin-bottom: 20px; padding: 15px; background: #fff3cd; border-radius: 8px; color: #856404; border: 1px solid #ffeaa7;"><strong>📋 Đáp án chi tiết</strong><br>Dưới đây là tất cả câu hỏi và đáp án đúng để bạn ôn tập.</div>';

            quizData.forEach((question, index) => {
                reviewHTML += `
                    <div class="review-question">
                        <div class="question-number">Câu ${index + 1}: <i class="fas fa-times-circle" style="color: #dc3545;"></i> Không trả lời</div>
                        <div class="question-text">${question.question}</div>
                        <div class="options">
                            ${question.options.map((option, optIndex) => {
                                let optionClass = '';
                                if (optIndex === question.correct) {
                                    optionClass = 'correct-answer';
                                }

                                return `<div class="${optionClass}">
                                    ${String.fromCharCode(65 + optIndex)}. ${option}
                                    ${optIndex === question.correct ? ' ✓' : ''}
                                </div>`;
                            }).join('')}
                        </div>
                        <div class="explanation">
                            <strong>Giải thích:</strong> ${question.explanation}
                        </div>
                    </div>
                `;
            });

            reviewContainer.innerHTML = reviewHTML;

            // Hide the cheating notice and show review
            const cheatingNotice = document.querySelector('.resultsContainer > div');
            if (cheatingNotice) {
                cheatingNotice.style.display = 'none';
            }
        }

        // Check if user has already completed this assignment
        async function checkAssignmentCompletion() {
            // Wait for auth state to be determined
            return new Promise((resolve) => {
                const unsubscribe = onAuthStateChanged(auth, async (user) => {
                    unsubscribe(); // Stop listening after first check

                    if (!user) {
                        alert('Bạn cần đăng nhập để làm bài tập!');
                        window.location.href = '../../../auth/';
                        return;
                    }

                    try {
                        console.log('Checking assignment 2 completion for user:', user.uid);
                        const assignmentDoc = await getDoc(doc(db, "users", user.uid, "assignments", "assignment-2"));

                        if (assignmentDoc.exists()) {
                            // User has already completed this assignment
                            console.log('Assignment 2 already completed, showing results');
                            const assignmentData = assignmentDoc.data();
                            console.log('Assignment 2 data:', assignmentData);
                            showCompletedAssignment(assignmentData);
                        } else {
                            // User hasn't completed this assignment yet
                            console.log('Assignment 2 not completed yet, initializing quiz');
                            initializeQuiz();
                        }
                    } catch (error) {
                        console.error("Error checking assignment 2 completion:", error);
                        // If there's an error, allow user to take the quiz
                        initializeQuiz();
                    }

                    resolve();
                });
            });
        }

        // Initialize quiz for new attempt
        async function initializeQuiz() {
            // Show confirmation dialog before starting
            const confirmed = await showQuizStartConfirmation();
            if (!confirmed) {
                // User cancelled, redirect back to class page
                window.location.href = '../../python-c.html';
                return;
            }

            quizStarted = true;
            setupAntiCheat(); // Setup anti-cheat monitoring

            // Reset timer display
            document.getElementById('timer').textContent = '30:00';
            document.getElementById('timer').style.color = '#4285F4';
            document.getElementById('current-question').textContent = '1';

            loadQuizData();
            startTimer();
            startTime = new Date();
        }

        // Show completed assignment results
        function showCompletedAssignment(assignmentData) {
            // Update timer to show completed status
            document.getElementById('timer').textContent = 'Đã hoàn thành';
            document.getElementById('timer').style.color = '#28a745';

            // Hide quiz container
            document.getElementById('quizContainer').style.display = 'none';

            // Show results container
            document.getElementById('resultsContainer').style.display = 'block';

            // Display score
            document.getElementById('finalScore').textContent = assignmentData.score;

            // Display completion time
            const minutes = Math.floor(assignmentData.completionTime / 60);
            const seconds = assignmentData.completionTime % 60;
            document.getElementById('completionTime').textContent =
                `${minutes}:${seconds.toString().padStart(2, '0')}`;

            // Display review
            const reviewContainer = document.getElementById('reviewContainer');
            let reviewHTML = '<div style="text-align: center; margin-bottom: 20px; padding: 15px; background: #e8f5e8; border-radius: 8px; color: #2e7d32;"><strong>✅ Bạn đã hoàn thành bài tập này!</strong><br>Dưới đây là kết quả và đáp án để bạn ôn tập.</div>';

            if (assignmentData.results) {
                assignmentData.results.forEach((result, index) => {
                    const statusClass = result.isCorrect ? 'correct-answer' : 'wrong-answer';
                    const statusIcon = result.isCorrect ? '<i class="fas fa-check-circle"></i>' : '<i class="fas fa-times-circle"></i>';
                    const statusText = result.isCorrect ? 'Đúng' : 'Sai';

                    reviewHTML += `
                        <div class="review-question">
                            <div class="question-number">Câu ${index + 1}: ${statusIcon} ${statusText}</div>
                            <div class="question-text">${result.question}</div>
                            <div class="options">
                                ${result.options.map((option, optIndex) => {
                                    let optionClass = '';
                                    if (optIndex === result.correctAnswer) {
                                        optionClass = 'correct-answer';
                                    } else if (optIndex === result.userAnswer && !result.isCorrect) {
                                        optionClass = 'wrong-answer';
                                    }

                                    return `<div class="${optionClass}">
                                        ${String.fromCharCode(65 + optIndex)}. ${option}
                                        ${optIndex === result.correctAnswer ? ' ✓' : ''}
                                        ${optIndex === result.userAnswer && !result.isCorrect ? ' ✗' : ''}
                                    </div>`;
                                }).join('')}
                            </div>
                            <div class="explanation">
                                <strong>Giải thích:</strong> ${result.explanation}
                            </div>
                        </div>
                    `;
                });
            }

            reviewContainer.innerHTML = reviewHTML;
        }

        // Load quiz data from external file
        async function loadQuizData() {
            try {
                // Import quiz data
                const module = await import('./quiz-data-2.js');
                quizData = module.default || quizQuestions2;
            } catch (error) {
                console.error('Error loading quiz data 2:', error);
                // Fallback to inline data if import fails
                quizData = quizQuestions2;
            }

            generateQuestionGrid();
            displayQuestion(0);
        }

        // Generate question grid indicators
        function generateQuestionGrid() {
            const grid = document.getElementById('questionGrid');
            grid.innerHTML = '';

            for (let i = 0; i < quizData.length; i++) {
                const indicator = document.createElement('div');
                indicator.className = 'question-indicator';
                indicator.textContent = i + 1;
                indicator.onclick = () => goToQuestion(i);
                grid.appendChild(indicator);
            }

            updateQuestionGrid();
        }

        // Update question grid indicators
        function updateQuestionGrid() {
            const indicators = document.querySelectorAll('.question-indicator');
            indicators.forEach((indicator, index) => {
                indicator.classList.remove('current', 'answered');

                if (index === currentQuestionIndex) {
                    indicator.classList.add('current');
                } else if (userAnswers[index] !== undefined) {
                    indicator.classList.add('answered');
                }
            });
        }

        // Display current question
        function displayQuestion(index) {
            const container = document.getElementById('questionsContainer');
            const question = quizData[index];

            container.innerHTML = `
                <div class="question active">
                    <div class="question-number">Câu ${index + 1}:</div>
                    <div class="question-text">${question.question}</div>
                    <ul class="options">
                        ${question.options.map((option, optIndex) => `
                            <li class="option ${userAnswers[index] === optIndex ? 'selected' : ''}"
                                onclick="selectAnswer(${index}, ${optIndex})">
                                <input type="radio" name="question${index}" value="${optIndex}"
                                       ${userAnswers[index] === optIndex ? 'checked' : ''}>
                                ${String.fromCharCode(65 + optIndex)}. ${option}
                            </li>
                        `).join('')}
                    </ul>
                </div>
            `;

            updateNavigation();
            updateProgress();
            updateQuestionGrid();
        }

        // Select answer
        function selectAnswer(questionIndex, optionIndex) {
            userAnswers[questionIndex] = optionIndex;

            // Update UI
            const options = document.querySelectorAll('.option');
            options.forEach((option, index) => {
                option.classList.remove('selected');
                if (index === optionIndex) {
                    option.classList.add('selected');
                }
            });

            // Update radio button
            const radio = document.querySelector(`input[name="question${questionIndex}"][value="${optionIndex}"]`);
            if (radio) radio.checked = true;

            updateQuestionGrid();
        }

        // Navigation functions
        function nextQuestion() {
            if (currentQuestionIndex < quizData.length - 1) {
                currentQuestionIndex++;
                displayQuestion(currentQuestionIndex);
            }
        }

        function previousQuestion() {
            if (currentQuestionIndex > 0) {
                currentQuestionIndex--;
                displayQuestion(currentQuestionIndex);
            }
        }

        function goToQuestion(index) {
            currentQuestionIndex = index;
            displayQuestion(currentQuestionIndex);
        }

        // Update navigation buttons
        function updateNavigation() {
            const prevBtn = document.getElementById('prevBtn');
            const nextBtn = document.getElementById('nextBtn');
            const questionStatus = document.getElementById('questionStatus');

            prevBtn.disabled = currentQuestionIndex === 0;
            nextBtn.style.display = currentQuestionIndex === quizData.length - 1 ? 'none' : 'block';

            questionStatus.textContent = `Câu ${currentQuestionIndex + 1} / ${quizData.length}`;

            document.getElementById('current-question').textContent = currentQuestionIndex + 1;
            document.getElementById('total-questions').textContent = quizData.length;
        }

        // Update progress bar
        function updateProgress() {
            const answeredCount = Object.keys(userAnswers).length;
            const progress = (answeredCount / quizData.length) * 100;
            document.getElementById('progressFill').style.width = progress + '%';
        }

        // Timer functions
        function startTimer() {
            timerInterval = setInterval(() => {
                timeLeft--;
                updateTimerDisplay();

                if (timeLeft <= 0) {
                    clearInterval(timerInterval);
                    submitQuiz();
                }
            }, 1000);
        }

        function updateTimerDisplay() {
            const minutes = Math.floor(timeLeft / 60);
            const seconds = timeLeft % 60;
            document.getElementById('timer').textContent =
                `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        }

        // Submit quiz function
        async function submitQuiz() {
            // Prevent multiple submissions
            if (document.getElementById('submitBtn').disabled) return;

            // Disable submit button immediately
            document.getElementById('submitBtn').disabled = true;
            document.getElementById('submitBtn').innerHTML = '<i class="fas fa-spinner fa-spin"></i> Đang nộp bài...';

            clearInterval(timerInterval);
            quizCompleted = true; // Mark quiz as completed to prevent page unload warning

            const endTime = new Date();
            const completionTime = Math.floor((endTime - startTime) / 1000); // in seconds

            // Calculate score
            let score = 0;
            const results = [];

            quizData.forEach((question, index) => {
                const userAnswer = userAnswers[index];
                const isCorrect = userAnswer !== undefined && userAnswer === question.correct;

                if (isCorrect) score++;

                results.push({
                    questionIndex: index,
                    question: question.question || '',
                    options: question.options || [],
                    userAnswer: userAnswer !== undefined ? userAnswer : -1, // Use -1 for no answer
                    correctAnswer: question.correct || 0,
                    isCorrect: isCorrect,
                    explanation: question.explanation || 'Không có giải thích'
                });
            });

            // Save results to Firebase
            await saveQuizResults(score, completionTime, results);

            // Update timer display to show completion
            document.getElementById('timer').textContent = 'Đã hoàn thành';
            document.getElementById('timer').style.color = '#28a745';

            // Disable all quiz interactions
            disableQuizInteractions();

            // Show results
            showResults(score, completionTime, results);
        }

        // Disable all quiz interactions after submission
        function disableQuizInteractions() {
            // Disable all option buttons
            const options = document.querySelectorAll('.option');
            options.forEach(option => {
                option.style.pointerEvents = 'none';
                option.style.opacity = '0.6';
            });

            // Disable navigation buttons
            const navButtons = document.querySelectorAll('.nav-btn');
            navButtons.forEach(btn => {
                btn.disabled = true;
                btn.style.opacity = '0.6';
            });

            // Disable question grid buttons
            const gridButtons = document.querySelectorAll('.question-grid button');
            gridButtons.forEach(btn => {
                btn.disabled = true;
                btn.style.opacity = '0.6';
            });
        }

        // Show results
        function showResults(score, completionTime, results) {
            document.getElementById('quizContainer').style.display = 'none';
            document.getElementById('resultsContainer').style.display = 'block';

            // Display score
            document.getElementById('finalScore').textContent = score;

            // Display completion time
            const minutes = Math.floor(completionTime / 60);
            const seconds = completionTime % 60;
            document.getElementById('completionTime').textContent =
                `${minutes}:${seconds.toString().padStart(2, '0')}`;

            // Display review
            const reviewContainer = document.getElementById('reviewContainer');
            let reviewHTML = '<div style="text-align: center; margin-bottom: 20px; padding: 15px; background: #e8f5e8; border-radius: 8px; color: #2e7d32;"><strong>🎉 Chúc mừng! Bạn đã nộp bài thành công!</strong><br>Bài học 2 đã được đánh dấu hoàn thành và điểm số đã được cập nhật vào bảng xếp hạng.</div>';

            results.forEach((result, index) => {
                const statusClass = result.isCorrect ? 'correct-answer' : 'wrong-answer';
                const statusIcon = result.isCorrect ? '<i class="fas fa-check-circle"></i>' : '<i class="fas fa-times-circle"></i>';
                const statusText = result.isCorrect ? 'Đúng' : 'Sai';

                reviewHTML += `
                    <div class="review-question">
                        <div class="question-number">Câu ${index + 1}: ${statusIcon} ${statusText}</div>
                        <div class="question-text">${result.question}</div>
                        <div class="options">
                            ${result.options.map((option, optIndex) => {
                                let optionClass = '';
                                if (optIndex === result.correctAnswer) {
                                    optionClass = 'correct-answer';
                                } else if (optIndex === result.userAnswer && !result.isCorrect) {
                                    optionClass = 'wrong-answer';
                                }

                                return `<div class="${optionClass}">
                                    ${String.fromCharCode(65 + optIndex)}. ${option}
                                    ${optIndex === result.correctAnswer ? ' ✓' : ''}
                                    ${optIndex === result.userAnswer && !result.isCorrect ? ' ✗' : ''}
                                </div>`;
                            }).join('')}
                        </div>
                        <div class="explanation">
                            <strong>Giải thích:</strong> ${result.explanation}
                        </div>
                    </div>
                `;
            });

            reviewContainer.innerHTML = reviewHTML;
        }

        // Save quiz results to Firebase
        async function saveQuizResults(score, completionTime, results) {
            const user = auth.currentUser;
            if (!user) return;

            // Admin can do assignments but results won't be saved
            if (user.email === '<EMAIL>') {
                console.log('🔧 Admin mode: Assignment 2 completed but results not saved');
                console.log('Admin score:', score);
                console.log('Admin completion time:', completionTime);
                return; // Exit early for admin
            }

            try {
                // Validate and sanitize all data before saving
                const sanitizedScore = typeof score === 'number' ? score : 0;
                const sanitizedCompletionTime = typeof completionTime === 'number' ? completionTime : 0;
                const sanitizedResults = Array.isArray(results) ? results.map(result => ({
                    questionIndex: typeof result.questionIndex === 'number' ? result.questionIndex : 0,
                    question: typeof result.question === 'string' ? result.question : 'No question',
                    options: Array.isArray(result.options) ? result.options : [],
                    userAnswer: typeof result.userAnswer === 'number' ? result.userAnswer : -1,
                    correctAnswer: typeof result.correctAnswer === 'number' ? result.correctAnswer : 0,
                    isCorrect: typeof result.isCorrect === 'boolean' ? result.isCorrect : false,
                    explanation: typeof result.explanation === 'string' ? result.explanation : 'No explanation'
                })) : [];

                const assignmentData = {
                    assignmentId: 'assignment-2',
                    assignmentTitle: 'Bài Tập 2: Python và Cài Đặt Môi Trường',
                    score: sanitizedScore,
                    totalQuestions: quizData && quizData.length ? quizData.length : 0,
                    completionTime: sanitizedCompletionTime,
                    completedAt: new Date().toISOString(),
                    results: sanitizedResults
                };

                // Debug log to check data before saving
                console.log('Saving assignment 2 data:', assignmentData);

                // Double-check if assignment already exists to prevent duplicate scoring
                const existingAssignment = await getDoc(doc(db, "users", user.uid, "assignments", "assignment-2"));
                if (existingAssignment.exists()) {
                    console.log('⚠️ Assignment 2 already exists, preventing duplicate save');
                    alert('Bài tập này đã được nộp trước đó. Không thể nộp lại.');
                    return;
                }

                // Save to user's assignments collection
                await setDoc(doc(db, "users", user.uid, "assignments", "assignment-2"), assignmentData);

                // Update user's assignment count and total score
                const userDoc = await getDoc(doc(db, "users", user.uid));
                if (userDoc.exists()) {
                    const userData = userDoc.data();
                    const currentTotalScore = userData.totalScore || 0;
                    const newTotalScore = currentTotalScore + score;

                    await updateDoc(doc(db, "users", user.uid), {
                        assignmentCount: (userData.assignmentCount || 0) + 1,
                        totalScore: newTotalScore,
                        lastAssignmentScore: score,
                        lastAssignmentDate: new Date().toISOString()
                    });

                    console.log(`Updated total score: ${currentTotalScore} + ${score} = ${newTotalScore}`);
                }

                console.log('Quiz 2 results saved successfully');

                // Redirect back to class page after a short delay to show results
                setTimeout(() => {
                    window.location.href = '../../python-c.html';
                }, 3000);
            } catch (error) {
                console.error('Error saving quiz 2 results:', error);
            }
        }

        // Make functions global
        window.submitQuiz = submitQuiz;
        window.nextQuestion = nextQuestion;
        window.previousQuestion = previousQuestion;
        window.goToQuestion = goToQuestion;
        window.selectAnswer = selectAnswer;
        window.showDetailedResults = showDetailedResults;

        // Authentication is now handled in checkAssignmentCompletion function
    </script>

    <script src="quiz-data-2.js"></script>
    <script src="../../../assets/js/script.js"></script>
</body>
</html>
