// Copy the same quiz data from python-a
// This ensures consistency across classes

// Import or copy the quiz data from python-a
// For now, we'll reference the same structure

const quizData = [
    // Same 50 questions as python-a/quiz-data-3.js
    // This file can be updated to have class-specific questions if needed
];

// For now, we'll use the same questions as Python A
// In the future, this can be customized for Python C class

// Export for use in quiz system
if (typeof module !== 'undefined' && module.exports) {
    module.exports = quizData;
}

// Note: The actual quiz implementation will load the questions
// This file serves as a placeholder for Python C specific questions
