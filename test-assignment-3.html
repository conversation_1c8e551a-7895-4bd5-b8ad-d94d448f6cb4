<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Assignment 3</title>
</head>
<body>
    <h1>Test Assignment 3 Firebase Save</h1>
    <button onclick="testSave()">Test Save Data</button>
    <div id="result"></div>

    <script type="module">
        import { initializeApp } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js';
        import { getFirestore, doc, setDoc } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js';
        import { getAuth, signInWithEmailAndPassword } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js';

        const firebaseConfig = {
            apiKey: "AIzaSyBKNo8y_MOKYc3f3UdNRFwcMgeLRW71WXA",
            authDomain: "classroom-web-48bc2.firebaseapp.com",
            projectId: "classroom-web-48bc2",
            storageBucket: "classroom-web-48bc2.firebasestorage.app",
            messagingSenderId: "446746787502",
            appId: "1:446746787502:web:48d5ffd5a0b2c6e043b73f",
            measurementId: "G-742XRP9E96"
        };

        const app = initializeApp(firebaseConfig);
        const db = getFirestore(app);
        const auth = getAuth(app);

        window.testSave = async function() {
            try {
                // Login first
                await signInWithEmailAndPassword(auth, '<EMAIL>', '123456');
                console.log('Logged in successfully');

                const user = auth.currentUser;
                if (!user) {
                    document.getElementById('result').innerHTML = 'Not logged in';
                    return;
                }

                // Test data similar to assignment-3
                const testData = {
                    score: 80,
                    totalQuestions: 50,
                    completionTime: "5 phút 30 giây",
                    timestamp: new Date(),
                    results: [
                        {
                            question: "Test question",
                            options: ["A", "B", "C", "D"],
                            userAnswer: 0,
                            correctAnswer: 0,
                            isCorrect: true,
                            explanation: "Test explanation"
                        }
                    ]
                };

                console.log('Saving test data:', testData);
                await setDoc(doc(db, "users", user.uid, "assignments", "test-assignment-3"), testData);
                
                document.getElementById('result').innerHTML = 'Save successful!';
                console.log('Save successful');
            } catch (error) {
                console.error('Error:', error);
                document.getElementById('result').innerHTML = 'Error: ' + error.message;
            }
        };
    </script>
</body>
</html>
